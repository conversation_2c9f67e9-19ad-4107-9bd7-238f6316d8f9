from docx import Document
from reportlab.pdfgen import canvas

def docx_to_pdf(docx_path, pdf_path):
    doc = Document(docx_path)
    c = canvas.Canvas(pdf_path)
    y = 750  # 初始纵坐标
    for para in doc.paragraphs:
        text = para.text
        if text.strip():
            c.drawString(50, y, text)
            y -= 20  # 每行间距
            if y < 50:  # 换页
                c.showPage()
                y = 750
    c.save()

# 使用示例
docx_path = 'input.docx'
pdf_path = 'output.pdf'
docx_to_pdf(docx_path, pdf_path)