import requests
import json

# 从项目设置中导入 SuperMap iServer 的相关配置信息
from my_project.settings import SUPERMAP_ISERVER_URL, SUPERMAP_ISERVER_USER, SUPERMAP_ISERVER_PASSWORD


class SupermapHelper:
    def __int__(self):
        # 这里可能是拼写错误，通常应该是 __init__ 方法，当前为空，没有实际的初始化操作
        pass

    @staticmethod
    def get_token():
        """
        获取 SuperMap iServer 的访问令牌。

        :return: 返回请求获取到的令牌信息（文本形式）
        """
        # 从配置中获取 SuperMap iServer 的主机地址
        supermap_iserver_host = SUPERMAP_ISERVER_URL
        # 从配置中获取 SuperMap iServer 的用户名
        supermap_iserver_user = SUPERMAP_ISERVER_USER
        # 从配置中获取 SuperMap iServer 的密码
        supermap_iserver_password = SUPERMAP_ISERVER_PASSWORD
        # 构建获取令牌的请求 URL
        url = "{}iserver/services/security/tokens.rjson".format(supermap_iserver_host)
        # 构建请求的 JSON 数据
        payload = json.dumps({
            "username": supermap_iserver_user,
            "password": supermap_iserver_password,
            "clientType": "RequestIP",
            "expiration": 10  # 令牌有效期为 10 分钟
        })
        # 请求头，这里为空
        headers = {}
        # 发送 POST 请求获取令牌
        response = requests.request("POST", url, headers=headers, data=payload)
        return response.text

    @staticmethod
    # 获取服务列表
    def get_service_list():
        """
        获取 SuperMap iServer 的服务列表。

        :return: 返回包含服务列表信息的 JSON 数据
        """
        # 获取 SuperMap iServer 的访问令牌
        token = SupermapHelper.get_token()
        # 从配置中获取 SuperMap iServer 的主机地址
        supermap_iserver_host = SUPERMAP_ISERVER_URL
        # 构建获取服务列表的请求 URL，包含令牌信息
        url = "{}iserver/manager/services.rjson?token={}".format(supermap_iserver_host, token)
        # 请求的数据，这里为空
        payload = {}
        # 请求头，这里为空
        headers = {}
        # 发送 GET 请求获取服务列表
        response = requests.request("GET", url, headers=headers, data=payload)
        # 将响应的文本数据解析为 JSON 格式
        json_data = json.loads(response.text)
        return json_data

    @staticmethod
    # 获取工作空间服务列表
    def get_workspace_service_list():
        """
        获取 SuperMap iServer 的工作空间服务列表。

        :return: 返回包含工作空间服务列表信息的 JSON 数据
        """
        # 获取 SuperMap iServer 的访问令牌
        token = SupermapHelper.get_token()
        # 从配置中获取 SuperMap iServer 的主机地址
        supermap_iserver_host = SUPERMAP_ISERVER_URL
        # 构建获取工作空间服务列表的请求 URL，包含令牌信息
        url = "{}iserver/manager/workspaces.rjson?token={}".format(supermap_iserver_host, token)
        # 请求的数据，这里为空
        payload = {}
        # 请求头，这里为空
        headers = {}
        # 发送 GET 请求获取工作空间服务列表
        response = requests.request("GET", url, headers=headers, data=payload)
        # 将响应的文本数据解析为 JSON 格式
        json_data = json.loads(response.text)
        return json_data

    @staticmethod
    def get_providers_list():
        """
        获取 SuperMap iServer 的数据提供者列表。

        :return: 返回包含数据提供者列表信息的 JSON 数据
        """
        # 获取 SuperMap iServer 的访问令牌
        token = SupermapHelper.get_token()
        # 构建获取数据提供者列表的请求 URL，包含令牌信息
        url = "{}/iserver/manager/providers.rjson?token={}".format(SUPERMAP_ISERVER_URL, token)
        # 请求的数据，这里为空
        payload = {}
        # 请求头，这里为空
        headers = {}
        # 发送 GET 请求获取数据提供者列表
        response = requests.request("GET", url, headers=headers, data=payload)
        # 将响应的文本数据解析为 JSON 格式
        json_data = json.loads(response.text)
        return json_data

    @staticmethod
    # 获取指定数据服务的数据源列表
    def get_datasource_list_of_data_service(componentName, interfaceName):
        """
        获取指定数据服务的数据源列表。

        :param componentName: 组件名称
        :param interfaceName: 接口名称
        :return: 返回数据源名称列表
        """
        # 从配置中获取 SuperMap iServer 的主机地址
        supermap_iserver_host = SUPERMAP_ISERVER_URL
        # 构建服务实例的路径
        service_instance_path = "{}/{}".format(componentName, interfaceName)
        # 构建获取数据源列表的请求 URL
        url = "{}iserver/services/{}/data/datasources.json".format(supermap_iserver_host, service_instance_path)
        # 请求的数据，这里为空
        payload = {}
        # 请求头，这里为空
        headers = {}
        # 发送 GET 请求获取数据源列表
        response = requests.request("GET", url, headers=headers, data=payload)
        # 将响应的文本数据解析为 JSON 格式
        json_data = json.loads(response.text)
        # 从 JSON 数据中提取数据源名称列表并返回
        return json_data["datasourceNames"]

    @staticmethod
    # 获取指定数据服务 - 数据源的数据集列表
    def get_dataset_list_of_data_service_by_datasource(componentName, interfaceName, datasourceName):
        """
        获取指定数据服务下指定数据源的数据集列表。

        :param componentName: 组件名称
        :param interfaceName: 接口名称
        :param datasourceName: 数据源名称
        :return: 返回数据集名称列表
        """
        # 从配置中获取 SuperMap iServer 的主机地址
        supermap_iserver_host = SUPERMAP_ISERVER_URL
        # 构建服务实例的路径
        service_instance_path = "{}/{}".format(componentName, interfaceName)
        # 构建获取数据集列表的请求 URL
        url = "{}iserver/services/{}/data/datasources/{}/datasets.json".format(supermap_iserver_host,
                                                                               service_instance_path, datasourceName)
        # 请求的数据，这里为空
        payload = {}
        # 请求头，这里为空
        headers = {}
        # 发送 GET 请求获取数据集列表
        response = requests.request("GET", url, headers=headers, data=payload)
        # 将响应的文本数据解析为 JSON 格式
        json_data = json.loads(response.text)
        # 从 JSON 数据中提取数据集名称列表并返回
        return json_data["datasetNames"]

    @staticmethod
    # 获取指定数据服务 - 数据源 - 数据集的要素记录信息
    def get_record_value_of_data_service_by_datasource_dataset(componentName, interfaceName, datasourceName,
                                                              datasetName):
        """
        获取指定数据服务下指定数据源和数据集的要素记录信息。

        :param componentName: 组件名称
        :param interfaceName: 接口名称
        :param datasourceName: 数据源名称
        :param datasetName: 数据集名称
        :return: 返回要素记录信息列表
        """
        # 从配置中获取 SuperMap iServer 的主机地址
        supermap_iserver_host = SUPERMAP_ISERVER_URL
        # 构建服务实例的路径
        service_instance_path = "{}/{}".format(componentName, interfaceName)
        # 构建获取要素记录信息的请求 URL
        url = "{}iserver/services/{}/data/featureResults.json?returnContent=true".format(supermap_iserver_host,
                                                                                         service_instance_path
                                                                                         )
        # 构建请求的 JSON 数据
        payload = json.dumps({
            "getFeatureMode": "SQL",
            "datasetNames": [
                "{}:{}".format(datasourceName, datasetName)
            ],
            "maxFeatures": 100,
            "queryParameter": {
                "sortClause": None,
                "ids": None,
                "name": None,
                "attributeFilter": None,
                "groupClause": None,
                "linkItems": None,
                "joinItems": None,
                "fields": None
            }
        })
        # 请求头，这里为空
        headers = {}
        # 发送 POST 请求获取要素记录信息
        response = requests.request("POST", url, headers=headers, data=payload)
        # 将响应的文本数据解析为 JSON 格式
        json_data = json.loads(response.text)
        # 从 JSON 数据中提取要素记录信息列表并返回
        return json_data["features"]

    @staticmethod
    # 启停服务下所有服务实例
    def set_service_status(name, status):
        """
        启停服务下的所有服务实例。

        :param name: 服务名称
        :param status: 状态值，1 表示启用，其他值表示停用
        :return: 返回操作结果的 JSON 数据
        """
        # 获取 SuperMap iServer 的访问令牌
        token = SupermapHelper.get_token()
        # 从配置中获取 SuperMap iServer 的主机地址
        supermap_iserver_host = SUPERMAP_ISERVER_URL
        # 构建设置服务状态的请求 URL，包含令牌信息
        url = "{}iserver/manager/services.rjson?token=".format(supermap_iserver_host, token)
        # 构建请求的 JSON 数据
        payload = json.dumps([
            {
                "name": "{}".format(name),
                "enabled": True if status == 1 else False
            }
        ])
        # 请求头，这里为空
        headers = {}
        # 发送 PUT 请求设置服务状态
        response = requests.request("PUT", url, headers=headers, data=payload)
        # 将响应的文本数据解析为 JSON 格式
        json_data = json.loads(response.text)
        # 返回操作结果的 JSON 数据，例如 {"succeed": true}
        return json_data

    @staticmethod
    # 启停服务下的单个服务实例
    def set_service_instance_status(componentName, interfaceName, status):
        """
        启停服务下的单个服务实例。

        :param componentName: 组件名称
        :param interfaceName: 接口名称
        :param status: 状态值，1 表示启用，其他值表示停用
        :return: 返回操作结果的 JSON 数据
        """
        # 获取 SuperMap iServer 的访问令牌
        token = SupermapHelper.get_token()
        # 从配置中获取 SuperMap iServer 的主机地址
        supermap_iserver_host = SUPERMAP_ISERVER_URL
        # 构建服务实例的路径
        service_instance_path = "{}/{}".format(componentName, interfaceName)
        # 构建设置服务实例状态的请求 URL，包含令牌信息
        url = "{}iserver/manager/instances/{}.rjson?token={}".format(supermap_iserver_host, service_instance_path,
                                                                     token)
        # 构建请求的 JSON 数据
        payload = json.dumps(
            {
                "name": "{}".format(service_instance_path),
                "enabled": True if status == 1 else False
            }
        )
        # 请求头，这里为空
        headers = {}
        # 发送 PUT 请求设置服务实例状态
        response = requests.request("PUT", url, headers=headers, data=payload)
        # 将响应的文本数据解析为 JSON 格式
        json_data = json.loads(response.text)
        # 返回操作结果的 JSON 数据，例如 {"succeed": true}
        return json_data

    @staticmethod
    # 获取服务下的单个服务实例的地图信息
    def get_maps_of_service_instance(componentName, interfaceName):
        """
        获取服务下的单个服务实例的地图信息。

        :param componentName: 组件名称
        :param interfaceName: 接口名称
        :return: 返回包含地图信息的 JSON 数据
        """
        # 获取 SuperMap iServer 的访问令牌
        token = SupermapHelper.get_token()
        # 从配置中获取 SuperMap iServer 的主机地址
        supermap_iserver_host = SUPERMAP_ISERVER_URL
        # 构建服务实例的路径
        service_instance_path = "{}/{}".format(componentName, interfaceName)
        # 构建获取地图信息的请求 URL，包含令牌信息
        url = "{}iserver/services/{}/maps.rjson?token={}".format(supermap_iserver_host, service_instance_path, token)
        # 请求的数据，这里为空
        payload = {}
        # 请求头，这里为空
        headers = {}
        # 发送 GET 请求获取地图信息
        response = requests.request("GET", url, headers=headers, data=payload)
        # 打印服务实例路径和响应信息，可用于调试
        print("{}/{}".format(componentName, interfaceName))
        print(response)
        # 将响应的文本数据解析为 JSON 格式
        json_data = json.loads(response.text)
        # 返回包含地图信息的 JSON 数据，例如 {"succeed": true}
        return json_data