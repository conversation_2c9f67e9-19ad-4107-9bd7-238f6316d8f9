import time
from django.db.backends.postgresql.base import DatabaseWrapper as OriginalDatabaseWrapper
from django.db.utils import OperationalError

class DatabaseWrapper(OriginalDatabaseWrapper):
    MAX_RETRIES = 3  # 最大重试次数
    RETRY_DELAY = 5  # 重试间隔（秒）

    @staticmethod
    def _cursor(self, *args, **kwargs):
        retries = 0
        while True:
            try:
                return super()._cursor(*args, **kwargs)
            except OperationalError as e:
                if retries >= self.MAX_RETRIES:
                    raise
                print(f"数据库重连 {self.RETRY_DELAY} seconds... ({retries + 1}/{self.MAX_RETRIES})")
                time.sleep(self.RETRY_DELAY)
                retries += 1