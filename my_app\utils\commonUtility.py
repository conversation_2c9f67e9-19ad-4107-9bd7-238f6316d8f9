#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
import logging
import os.path
import re

import chardet
from bs4 import BeautifulSoup
from docx import Document

from my_project import settings

# python dict对象帮助类

# 获取 Django 日志记录器
logger = logging.getLogger('django')
import requests
import json
import datetime

from toollib.guid import SnowFlake


class SnowflakeIDUtil:
    @staticmethod
    # 生成雪花ID不超过16位，保证前端能够正常传输
    def snowflakeId():
        """
        生成不超过16位的雪花ID，确保该ID能在前端正常传输。

        :return: 生成的雪花ID
        """
        # 初始化 SnowFlake 类，设置 worker_id_bits 和 datacenter_id_bits 为 0
        snow = SnowFlake(worker_id_bits=0, datacenter_id_bits=0)
        # 调用 gen_uid 方法生成雪花ID
        return snow.gen_uid()


class CommonHelper:
    def __init__(self):
        # 类的初始化方法，当前为空，可根据需求添加初始化逻辑
        pass

    @staticmethod
    # 查找并获取 enabled 值
    def get_enabled_by_service_name(data, target_service_name):
        """
        在给定的数据列表中查找指定服务名称对应的 enabled 值。

        :param data: 包含服务信息的列表，列表中的每个元素是一个字典，字典应包含 "serviceName" 和 "enabled" 键
        :param target_service_name: 要查找的目标服务名称
        :return: 如果找到对应的服务名称，返回其 enabled 值；否则返回 None
        """
        # 遍历数据列表
        for item in data:
            # 检查当前元素的 "serviceName" 是否等于目标服务名称
            if item["serviceName"] == target_service_name:
                # 若相等，返回该元素的 "enabled" 值
                return item["enabled"]
        # 若未找到，返回 None
        return None

    @staticmethod
    # 获取图片或文档的http头
    def get_url_head():
        """
        根据项目设置生成图片或文档的 HTTP 头。

        :return: 生成的 HTTP 头字符串
        """
        # 从项目设置中获取协议、服务 IP、服务端口和静态 URL 信息，拼接成 HTTP 头
        url_head = "{}://{}:{}{}".format(settings.PROJECT_WEB_PROTOCOL, settings.PROJECT_SERVICE_IP,
                                         settings.PROJECT_SERVICE_PORT, settings.STATIC_URL)
        return url_head

    @staticmethod
    # 将POST,GET的参数输出到日志
    def logger_json_key_value(request, logger):
        """
        将请求（POST 或 GET）中的参数信息输出到日志。

        :param request: Django 的请求对象
        :param logger: 日志记录器对象
        """
        if request.method == 'POST':
            # 若为 POST 请求，获取请求数据
            json_data = request.data
        elif request.method == "GET":
            # 若为 GET 请求，获取查询参数
            json_data = request.query_params
        # 遍历参数键值对
        for key, value in json_data.items():
            # 将键值对信息记录到日志中
            logger.info("{}:{}".format(key, value))

    @staticmethod
    def get_curent_time_str():
        """
        获取当前时间的字符串表示，格式为 "YYYY-MM-DD HH:MM:SS"。

        :return: 当前时间的字符串
        """
        # 获取当前时间
        now_time = datetime.datetime.now()
        # 将当前时间格式化为指定字符串格式
        now_time = now_time.strftime("%Y-%m-%d %H:%M:%S")
        return now_time

    @staticmethod
    def get_token_of_ai_service():
        """
        获取 AI 服务的令牌，当前方法为空，可根据实际需求实现具体逻辑。

        :return: 无返回值，需补充逻辑
        """
        pass

    @staticmethod
    # 对文档分析的问题里的网址文字替换为网址抓取后生成的文档名称
    def replace_file_anlysis_message_content(content, replace):
        """
        将文档分析问题内容中的网址相关文字替换为指定的内容（通常是网址抓取后生成的文档名称）。

        :param content: 包含网址相关文字的文档分析问题内容
        :param replace: 要替换的目标内容，通常是网址抓取后生成的文档名称
        :return: 替换后的内容，同时去除开头多余的逗号
        """
        # 替换文件分析内容
        if "网页链接" in content:
            # 若内容中包含 "网页链接"，将其替换为指定内容
            content = content.replace("网页链接", replace)
        if "网址链接" in content:
            # 若内容中包含 "网址链接"，将其替换为指定内容
            content = content.replace("网址链接", replace)
        if "链接" in content:
            # 若内容中包含 "链接"，将其替换为指定内容
            content = content.replace("链接", replace)
        if "网址" in content:
            # 若内容中包含 "网址"，将其替换为指定内容
            content = content.replace("网址", replace)
        # 去除内容开头多余的逗号
        content = content.lstrip(",").lstrip(",")
        return content

    @staticmethod
    def is_has_str_in_respoonse(match_str, response):
        """
        检查响应内容中是否包含指定的字符串模式。

        :param match_str: 要匹配的字符串模式
        :param response: 响应内容字符串
        :return: 如果包含指定模式，返回 True；否则返回 False
        """
        is_has = False
        # 构建正则表达式模式
        pattern = r'\{}(.*).'.format(match_str)
        # 使用 re.search 方法在响应内容中查找匹配模式
        if re.search(pattern, response):
            is_has = True
        return is_has

    @staticmethod
    def get_json_from_response(match_str, response):
        """
        从响应内容中提取匹配指定模式后的 JSON 数据。

        :param match_str: 要匹配的字符串模式
        :param response: 响应内容字符串
        :return: 提取的 JSON 数据
        """
        # 构建正则表达式模式
        pattern = r'\{}(.*).'.format(match_str)
        # 使用 re.search 方法在响应内容中查找匹配模式
        match = re.search(pattern, response)
        if match and len(match.groups()) > 0:
            # 若匹配成功且有捕获组，获取捕获组内容
            gpt = match.group(1)  # 这是[---report_response_end--]之后的内容

        if 'gpt' in locals():
            # 若 gpt 变量存在
            last_char = gpt[-1]
            if last_char != '}':
                # 若最后一个字符不是 '}'，添加 '}'
                gpt += "}"
            # 将单引号替换为双引号
            gpt = gpt.replace("'", '"')
            # 将字符串解析为 JSON 数据
            json_data = json.loads(gpt)
        return json_data

    @staticmethod
    # 支持文本型网址
    def get_doc_from_web_page(url, doc_root):
        """
        从指定的网页地址抓取内容，并将其保存为 Word 文档。

        :param url: 要抓取的网页地址
        :param doc_root: 保存 Word 文档的根目录
        :return: 若抓取成功，返回保存的 Word 文档的路径；否则返回 None
        """
        # 获取网页内容
        # url = 'https://www.mee.gov.cn/xxgk2018/xxgk/xxgk03/202403/t20240312_1068223.html?keywords='  # 替换为你要抓取的网页地址
        # 发送 GET 请求获取网页内容
        response = requests.get(url)
        if response.status_code == 200:
            # 若请求成功
            # 检测网页内容的编码格式
            detected_encoding = chardet.detect(response.content)['encoding']
            # 使用 BeautifulSoup 解析网页内容
            soup = BeautifulSoup(response.content, 'lxml', from_encoding=detected_encoding)
            # 创建一个 Document 对象
            doc = Document()
            # 获取网页标题
            title = soup.title.string
            # 添加标题到 Word 文档
            doc.add_heading(title, 0)

            # 解析 HTML 内容并添加到 Word 文档
            for paragraph in soup.find_all('p'):
                # 遍历网页中的所有段落标签，将其文本内容添加到 Word 文档中
                doc.add_paragraph(paragraph.get_text())

            # 保存 Word 文档
            doc_path = os.path.join(doc_root, '{}.docx'.format(title))
            doc.save(doc_path)
            return doc_path
        else:
            # 若请求失败，返回 None
            return None

    #  替换 gpt 返回信息里的标记
    @staticmethod
    def remove_annotations(text):
        """
        移除 GPT 返回信息里的特定标记，如【4:4†source】。

        :param text: 包含特定标记的文本
        :return: 移除标记后的文本
        """
        # 正则表达式匹配格式，例如：【4:4†source】,其中数组是 1
        ip_pattern = r'【\d{1,3}:\d{1,3}†source】'
        # 使用 re.sub 进行全局替换，将匹配的标记替换为空字符串
        return re.sub(ip_pattern, '', text)


# 单元测试
# 由于引入了django的settings，所以没法做单元测试了
if __name__ == '__main__':
    # [---chat_response_end--]{'upload_file_ids':['file-5jWU4iO0wtAOUa135DoJqOmQ'],'assistant_id':'asst_ZNRuHfiaWvuJ6mG9IPmnAVa6','thread_id'：'thread_M4sLm9vCaAw7jZXeIJJAPYsD'}
    teststr = " [---chat_response_end--]{'assistant_id':'asst_98OEqjlTqXsOC98OPHu502UB','thread_id':'thread_gcPmHu9783TVZySIiYmIVKsJ'}"

    checkstr = "[---chat_response_end--]"

    print(CommonHelper.is_has_str_in_respoonse(checkstr, teststr))
    print(CommonHelper.get_json_from_response(checkstr, teststr)["assistant"])
    # date_text = "202338-43044-54"
    # CommonHelper.is_date_str(date_text)
    # date_text = "2023-4-15"
    # CommonHelper.is_date_str(date_text)
    # date_text = "2023 4 15"
    # CommonHelper.is_date_str(date_text)
    # date_text = "2023-2-23"
    # CommonHelper.convert_date(date_text)
    # date_text = "2月23日"
    # CommonHelper.convert_date(date_text)
    # date_text = "2015年2月23日"
    # CommonHelper.convert_date(date_text)
    # 133,988,250 USD 转为 133988250
    # $1,531,180.00 转换为 1531180.00

    # currency_text = "133,988,250 USD"
    # CommonHelper.parse_currency(currency_text)
    # currency_text = "$1,531,180.00"
    # CommonHelper.parse_currency(currency_text)
    # currency_text = "ww1,531,180.00"
    # CommonHelper.parse_currency(currency_text)
    # currency_text = "1,531,180.00oo"
    # CommonHelper.parse_currency(currency_text)
    # currency_text = "1,531,180.00"
    # CommonHelper.parse_currency(currency_text)
    # currency_text = "&1531180.00"
    # CommonHelper.parse_currency(currency_text)
    # currency_text = "1,531,180.00"
    # CommonHelper.deecimal_currency(currency_text)

    # currency_text = None
    # CommonHelper.parse_currency(currency_text)
    #
    # currency_text = ""
    # CommonHelper.parse_currency(currency_text)
    #
    # currency_value = 1531180.00
    # CommonHelper.thousand_sep_currency(currency_value)
    #
    # currency_value = 15311443480
    # CommonHelper.thousand_sep_currency(currency_value)
    #
    # currency_value = 15311443480
    # curreny_unit = "$"
    # unit_position = "head"
    # CommonHelper.thousand_sep_currency_add_unit(currency_value, curreny_unit, unit_position)
    #
    # currency_value = 1531144344580
    # curreny_unit = "USD"
    # unit_position = "end"
    # CommonHelper.thousand_sep_currency_add_unit(currency_value, curreny_unit, unit_position)
    #
    # currency_value = 1531147743480
    # curreny_unit = None
    # unit_position = None
    # CommonHelper.thousand_sep_currency_add_unit(currency_value, curreny_unit, unit_position)

    # date_str = "2023年12月22日"
    # print(CommonHelper.parse_date(date_str))
    # date_str = "12月22日"
    # print(CommonHelper.parse_date(date_str))
    # date_str = "2023-12-22"
    # print(CommonHelper.parse_date(date_str))
