#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time : 2024-12-26 当前系统时间
# <AUTHOR> zhous
# @Email : <EMAIL>
# @File : alert_serializers.py
# @Descr : 后台管理预警序列化器
# @Software: VSCode

from rest_framework import serializers
from my_app.models import TtDroneAlerts

class AlertAdminSerializer(serializers.ModelSerializer):
    """预警管理序列化器 - 后台管理专用"""

    class Meta:
        model = TtDroneAlerts
        exclude = ['created_by_id', 'updated_by_id']
