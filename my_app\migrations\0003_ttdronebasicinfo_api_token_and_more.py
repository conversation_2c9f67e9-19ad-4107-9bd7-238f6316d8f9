# Generated by Django 4.2.2 on 2025-08-26 13:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('my_app', '0002_ttdronebasicinfo_deployment_address_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='api_token',
            field=models.CharField(blank=True, db_comment='API访问令牌', max_length=500),
        ),
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='connection_status',
            field=models.CharField(db_comment='连接状态', default='disconnected', max_length=20),
        ),
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='gimbal_stream_url',
            field=models.URLField(blank=True, db_comment='云台摄像头视频流'),
        ),
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='last_heartbeat',
            field=models.DateTimeField(blank=True, db_comment='最后心跳时间', null=True),
        ),
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='mqtt_endpoint',
            field=models.URLField(blank=True, db_comment='MQTT连接地址'),
        ),
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='nest_indoor_stream_url',
            field=models.URLField(blank=True, db_comment='巢内监控流'),
        ),
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='nest_outdoor_stream_url',
            field=models.URLField(blank=True, db_comment='巢外监控流'),
        ),
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='websocket_endpoint',
            field=models.URLField(blank=True, db_comment='WebSocket连接地址'),
        ),
        migrations.AddField(
            model_name='ttdronetasks',
            name='ai_analysis_result',
            field=models.TextField(db_comment='AI分析结果摘要，JSON格式字符串', default='{}'),
        ),
        migrations.AddField(
            model_name='ttdronetasks',
            name='analysis_end_time',
            field=models.DateTimeField(blank=True, db_comment='分析结束时间', null=True),
        ),
        migrations.AddField(
            model_name='ttdronetasks',
            name='analysis_start_time',
            field=models.DateTimeField(blank=True, db_comment='分析开始时间', null=True),
        ),
        migrations.AddField(
            model_name='ttdronetasks',
            name='review_notes',
            field=models.TextField(blank=True, db_comment='审核备注'),
        ),
        migrations.AddField(
            model_name='ttdronetasks',
            name='review_status',
            field=models.CharField(choices=[('pending', '待审核'), ('approved', '已通过'), ('rejected', '已拒绝'), ('revision_required', '需修改')], db_comment='审核状态', db_index=True, default='pending', max_length=20),
        ),
        migrations.AddField(
            model_name='ttdronetasks',
            name='review_time',
            field=models.DateTimeField(blank=True, db_comment='审核时间', null=True),
        ),
        migrations.AddField(
            model_name='ttdronetasks',
            name='reviewer_id',
            field=models.IntegerField(blank=True, db_comment='审核人ID', null=True),
        ),
        migrations.AlterField(
            model_name='ttdronetasks',
            name='task_status',
            field=models.CharField(choices=[('pending_review', '待审核'), ('pending', '待执行'), ('running', '执行中'), ('pending_analysis', '待分析'), ('analyzing', '分析中'), ('completed', '已完成'), ('cancelled', '已取消'), ('failed', '执行失败')], db_comment='任务状态', db_index=True, default='pending_review', max_length=20),
        ),
        migrations.CreateModel(
            name='TtFlightSchedule',
            fields=[
                ('schedule_id', models.BigAutoField(db_comment='排期ID', primary_key=True, serialize=False)),
                ('schedule_name', models.CharField(db_comment='排期名称', max_length=255)),
                ('planned_start_time', models.DateTimeField(db_comment='计划开始时间')),
                ('planned_end_time', models.DateTimeField(db_comment='计划结束时间')),
                ('estimated_duration', models.IntegerField(db_comment='预计飞行时间（分钟）')),
                ('route_geojson', models.TextField(db_comment='飞行路线GeoJSON格式')),
                ('waypoints_count', models.IntegerField(db_comment='航点数量')),
                ('total_distance', models.DecimalField(db_comment='总距离（米）', decimal_places=2, max_digits=10)),
                ('schedule_status', models.CharField(choices=[('planned', '已计划'), ('executing', '执行中'), ('completed', '已完成'), ('cancelled', '已取消')], db_comment='排期状态', db_index=True, default='planned', max_length=20)),
                ('actual_start_time', models.DateTimeField(blank=True, db_comment='实际开始时间', null=True)),
                ('actual_end_time', models.DateTimeField(blank=True, db_comment='实际结束时间', null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, db_comment='更新时间')),
                ('drone', models.ForeignKey(db_comment='执行无人机', on_delete=django.db.models.deletion.CASCADE, related_name='flight_schedules', to='my_app.ttdronebasicinfo')),
                ('task', models.OneToOneField(db_comment='关联任务', on_delete=django.db.models.deletion.CASCADE, related_name='flight_schedule', to='my_app.ttdronetasks')),
            ],
            options={
                'db_table': 'tt_flight_schedule',
                'db_table_comment': '航线排期表',
                'managed': True,
                'indexes': [models.Index(fields=['schedule_status'], name='idx_schedule_status'), models.Index(fields=['drone', 'schedule_status'], name='idx_drone_schedule_status'), models.Index(fields=['task'], name='idx_task_schedule'), models.Index(fields=['planned_start_time'], name='idx_planned_start_time'), models.Index(fields=['create_time'], name='idx_schedule_create_time')],
            },
        ),
    ]
