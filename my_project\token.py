#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
# @Time    :  2022/6/14 13:49
# <AUTHOR> chenxw
# @Email   : <EMAIL>
# @File    : token.py.py
# @Descr   : 
# @Software: PyCharm
import datetime

from django.core.cache import cache
# from django.utils.translation import ugettext_lazy as _
from rest_framework import HTTP_HEADER_ENCODING
from rest_framework import exceptions
from rest_framework.authentication import BaseAuthentication
from rest_framework.authtoken.models import Token

from my_app.models import SysParam
# 获取请求头里的token信息
from my_project.settings import AUTH_TOKEN_AGE, TOKEN_KEY, TOKEN_USE_CACHE
from my_project.utils import TimeUtil


def get_AUTH_TOKEN_AGE():
    obj = SysParam.objects.get(param_en_key='AUTH_TOKEN_AGE')
    if obj is not None:
        return int(obj.param_value)
    else:
        #用户在线时长，单位分钟
        return 30800


def get_TOKEN_KEY():
    obj = SysParam.objects.get(param_en_key='TOKEN_KEY')
    if obj is not None:
        return obj.param_value
    else:
        return 'Authorization'

def get_TOKEN_USE_CACHE():
    obj = SysParam.objects.get(param_en_key='TOKEN_USE_CACHE')
    if obj is not None:
        return True if obj.param_value == "是" else False
    else:
        return False

def get_authorization_header(request):
    """
    Return request's 'Authorization:' header, as a bytestring.

    Hide some test client ickyness where the header can be unicode.
    """
    auth = request.META.get('HTTP_AUTHORIZATION', b'')
    if isinstance(auth, type('')):
        # Work around django test client oddness
        auth = auth.encode(HTTP_HEADER_ENCODING)
    return auth


# 自定义的ExpiringTokenAuthentication认证方式
class ExpiringTokenAuthentication(BaseAuthentication):
    model = Token

    def authenticate(self, request):
        auth = get_authorization_header(request)

        if not auth:
            return None
        try:
            token = auth.decode()
        except UnicodeError:
            msg = _('Invalid token header. Token string should not contain invalid characters.')
            raise exceptions.AuthenticationFailed(msg)
        return self.authenticate_credentials(token)

    def authenticate_credentials(self, key):

        # 将 "Token <key>" 格式的字符串分割成 ['Token', '<key>']
        parts = key.split()

        # 检查格式是否正确
        if not parts or parts[0].lower() != 'token' or len(parts) != 2:
            raise exceptions.AuthenticationFailed('无效的Token头格式，应为 "Token <your_token>"')

        # 获取真正的 token 值
        token_key = parts[1]

        # if TOKEN_USE_CACHE:
        if get_TOKEN_USE_CACHE():
            # 增加了缓存机制
            # 首先先从缓存中查找
            token_cache = 'token_' + token_key
            cache_user = cache.get(token_cache)
            if cache_user:
                return (cache_user.user, cache_user)  # 首先查看token是否在缓存中，若存在，直接返回用户
        try:
            # 使用解析出的 token_key 进行查询
            token = self.model.objects.get(key=token_key)

        except self.model.DoesNotExist:
            raise exceptions.AuthenticationFailed('认证失败')

        if not token.user.is_active:
            raise exceptions.AuthenticationFailed('用户被禁止')

        now = int(TimeUtil.string2time_stamp(str(datetime.datetime.now())))
        token_created = int(TimeUtil.string2time_stamp(str(token.created)))

        # 满足条件的话，就表示token已失效，提示用户重新登录刷新token.
        # if now - token_created > AUTH_TOKEN_AGE:
        if now - token_created > get_AUTH_TOKEN_AGE():
            old_token = self.model.objects.filter(key=token_key)
            user_id=old_token[0].user_id
            old_token.delete()
            # # 删除登录的保险类型记录信息
            # old_insurance = SysUserLogin.objects.filter(user_id=token.user_id)
            # old_insurance.delete()
            raise exceptions.AuthenticationFailed('认证信息过期')
        # if TOKEN_USE_CACHE:
        if get_TOKEN_USE_CACHE():
            if token:
                token_cache = 'token_' + token_key
                # cache.set(token_cache, token, AUTH_TOKEN_AGE)  # 添加 token_xxx 到缓存
                cache.set(token_cache, token, get_AUTH_TOKEN_AGE())  # 添加 token_xxx 到缓存
        return (token.user, token)

    def authenticate_header(self, request):
        # return 'Authorization'
        # return TOKEN_KEY
        return get_TOKEN_KEY()
