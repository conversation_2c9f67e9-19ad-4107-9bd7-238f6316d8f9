# 建立 python3.8 环境
# FROM python38_django422_img:v1.0
    
# 镜像作者
# MAINTAINER CHENXW
    
# 设置 python 环境变量
# ENV PYTHONUNBUFFERED 1
    
# 设置pip源为国内源
# COPY pip.conf ~/.pip/pip.conf

# 拷贝字体文件
# COPY simfang.ttf /usr/share/fonts/chinese/simfang.ttf
    
## 在容器内创建zxdservice文件夹
# RUN mkdir -p /var/backend/cysxydservice
#
## 设置容器内工作目录
# WORKDIR /var/backend/cysxydservice
#
## 将当前目录文件加入到容器工作目录中（. 表示当前宿主机目录）
# ADD . /var/backend/cysxydservice
    
# pip安装依赖
#RUN pip install -r requirements.txt
#RUN pip install -i https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host mirrors.aliyun.com --default-timeout=60 --no-cache-dir -r requirements.txt


# 设置时区
# ENV TZ=Asia/Shanghai

# RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置启动
# CMD python3 manage.pyc runserver 0.0.0.0:10789

# 使用gldp_env_images镜像作为基础镜像
FROM myproject_base_images:latest

# 设置工作目录
WORKDIR /app

# 将当前目录下的所有文件复制到工作目录
COPY gzwj_license.lic /home/<USER>/license/
COPY . .
COPY logTools.py /usr/local/lib/python3.9/site-packages/vgis_log/logTools.py

# 安装 requirements.txt 中指定的依赖
# RUN pip install --upgrade pip
# RUN pip install --no-cache-dir -r requirements.txt

# 暴露端口 (可选，如果您的应用程序需要监听端口)
EXPOSE 10742

# 定义启动命令 (根据您的项目需要)
CMD ["python", "manage.py","runserver","0.0.0.0:10742"]
