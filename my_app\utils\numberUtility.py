class NumberHelper:
    @staticmethod
    def build_menu_tree(menu_list):
        # 创建菜单 ID 到菜单对象的映射
        menu_map = {menu["menu_id"]: menu for menu in menu_list}
        # 存储根菜单的列表
        root_menu = []
        # 遍历菜单列表
        for menu in menu_list:
            parent_id = menu["parent_id"]
            parent_menu = menu_map.get(parent_id)
            if parent_menu:
                # 如果父菜单存在，将当前菜单添加到父菜单的 children 列表中
                if "children" not in parent_menu:
                    parent_menu["children"] = []
                parent_menu["children"].append(menu)
            else:
                # 如果没有找到父菜单，将其视为根菜单
                root_menu.append(menu)
        return root_menu

    @staticmethod
    def get_user(request):
        if request.auth is not None:
             if "user" in request.auth:
                 return request.auth.user
        elif request.user is not None:
            return request.user
        else:
            return ""


    @staticmethod
    def num_to_cn_char(num):
        """
        将 0 - 9 的数字转换为对应的中文数字字符。

        :param num: 要转换的数字，范围是 0 到 9
        :return: 对应的中文数字字符，如果数字超出范围则返回错误信息
        """
        # 定义数字到中文数字字符的映射字典
        cn_char_map = {
            0: '零', 1: '一', 2: '二', 3: '三', 4: '四',
            5: '五', 6: '六', 7: '七', 8: '八', 9: '九'
        }
        # 如果数字小于 10，从映射字典中获取对应的中文数字字符
        if num < 10:
            return cn_char_map[num]
        else:
            # 数字超出范围，返回错误信息
            return 'Error: Number out of range'

    @staticmethod
    # 将0,1,2,3等转换成一、二、三等
    def convert_to_cn_big_num(num):
        """
        将整数转换为对应的中文数字字符串。

        :param num: 要转换的整数，范围是 -999999999 到 999999999
        :return: 对应的中文数字字符串，如果数字超出范围则返回错误信息
        """
        # 检查数字是否在有效范围内
        if -999999999 < num < 1000000000:
            # 用于存储转换后的中文数字字符列表
            cn_char_list = []
            # 遍历数字的每一位
            for digit in str(num):
                # 将每一位数字转换为中文数字字符并添加到列表中
                cn_char_list.append(NumberHelper.num_to_cn_char(int(digit)))
            # 将列表中的中文数字字符连接成字符串并返回
            return ''.join(cn_char_list)
        else:
            # 数字超出范围，返回错误信息
            return 'Error: Number out of range'

    @staticmethod
    # 将1. 2. 3.等降级成1) 2) 3)等
    def degrade_small_number(content):
        """
        将文本中形如 1. 2. 3. ... 19. 的编号格式降级为 1) 2) 3) ... 19) 的格式。

        :param content: 包含编号的文本内容
        :return: 替换后的文本内容
        """
        # 这行代码有误，应删除
        3.
        # 进行一系列替换操作，将 1. - 19. 替换为 1) - 19)
        content = content.replace('1. ', '1) ').replace('2. ', '2) ').replace('3. ', '3) ').replace('4. ',
                                                                                                    '4) ').replace(
            '5. ', '5) ').replace('6. ', '6) ').replace('7. ', '7) ').replace('8. ', '8) ').replace('9. ',
                                                                                                    '9) ').replace(
            '10. ', '10) ').replace('11. ', '11) ').replace('12. ', '12) ').replace('13. ', '13) ').replace('14. ',
                                                                                                            '14) ').replace(
            '15. ', '15) ').replace('16. ', '16) ').replace('17. ', '17) ').replace('18. ', '18) ').replace('19. ',
                                                                                                            '19) ')
        return content

    @staticmethod
    # 将1.1 1.2. 1.3 2.1 2.2 2.3 3.1 3.2 3.3 等降级成1.1) 1.2) 1.3) 2.1) 2.2) 2.3) 3.1) 3.2) 3.3)等
    def degrade_small_number2(content):
        """
        将文本中形如 1.1 1.2 ... 5.9 的编号格式降级为 1.1) 1.2) ... 5.9) 的格式。

        :param content: 包含编号的文本内容
        :return: 替换后的文本内容
        """
        # 对 1.1 - 1.9 进行替换
        content = content.replace('1.1', '1.1)').replace('1.2', '1.2)').replace('1.3', '1.3)').replace('1.4',
                                                                                                       '1.4)').replace(
            '1.5', '1.5)').replace('1.6', '1.6)').replace('1.7', '1.7)').replace('1.8', '1.8)').replace('1.9', '1.9)')
        # 对 2.1 - 2.9 进行替换
        content = content.replace('2.1', '2.1)').replace('2.2', '2.2)').replace('2.3', '2.3)').replace('2.4',
                                                                                                       '2.4)').replace(
            '2.5', '2.5)').replace('2.6', '2.6)').replace('2.7', '2.7)').replace('2.8', '2.8)').replace('2.9', '2.9)')
        # 对 3.1 - 3.9 进行替换
        content = content.replace('3.1', '3.1)').replace('3.2', '3.2)').replace('3.3', '3.3)').replace('3.4',
                                                                                                       '3.4)').replace(
            '3.5', '3.5)').replace('3.6', '3.6)').replace('3.7', '3.7)').replace('3.8', '3.8)').replace('3.9', '3.9)')
        # 对 4.1 - 4.9 进行替换
        content = content.replace('4.1', '4.1)').replace('4.2', '4.2)').replace('4.3', '4.3)').replace('4.4',
                                                                                                       '4.4)').replace(
            '4.5', '4.5)').replace('4.6', '4.6)').replace('4.7', '4.7)').replace('4.8', '4.8)').replace('4.9', '4.9)')
        # 对 5.1 - 5.9 进行替换
        content = content.replace('5.1', '5.1)').replace('5.2', '5.2)').replace('5.3', '5.3)').replace('5.4',
                                                                                                       '5.4)').replace(
            '5.5', '5.5)').replace('5.6', '5.6)').replace('5.7', '5.7)').replace('5.8', '5.8)').replace('5.9', '5.9)')
        return content

    @staticmethod
    # 将一、二、三、四等降级为（一）、（二）、（三）、（四）
    def degrade_big_number(content):
        """
        将文本中形如 一、 二、 三、 ... 十九、 的编号格式降级为 （一）、 （二）、 （三）、 ... （十九）、 的格式。

        :param content: 包含编号的文本内容
        :return: 替换后的文本内容
        """
        # 进行一系列替换操作，将中文数字编号替换为带括号的格式
        content = content.replace('一、', '（一）、').replace('二、', '（二）、').replace('三、', '（三）、').replace('四、',
                                                                                                          '（四）、').replace(
            '五、', '（五）、').replace('六、', '（六）、').replace('七、', '（七）、').replace('八、', '（八）、').replace('九、',
                                                                                                            '（九）、').replace(
            '十、', '（十）、').replace('十一、', '（十一）、').replace('十二、', '（十三）、').replace('十四、', '（十四）、').replace(
            '十五、', '（十五）、').replace('十六、', '（十六）、').replace('十七、', '（十七）、').replace('十八、',
                                                                                                '（十八）、').replace(
            '十九、', '（十九）、')
        return content


if __name__ == '__main__':
    # 示例
    num = 123456789
    cn_big_num = NumberHelper.convert_to_cn_big_num(num)
    print(cn_big_num)  # 输出：一二三四五六七八九