# -*- coding: utf-8 -*-
"""
无人机业务逻辑服务
负责处理无人机相关的数据生成、处理和业务逻辑
将业务逻辑从WebSocket Consumer中分离，保持代码清晰
"""
import random
import datetime
import math
from typing import Dict, List, Any, Optional
from loguru import logger


class DroneDataService:
    """
    无人机数据服务类
    负责生成和处理无人机相关的模拟数据

    优化说明：
    - 所有无人机假设处于飞行状态
    - 位置数据显示连续的飞行轨迹
    - 各项状态数据保持逻辑一致性

    TODO: 未来需要对接大疆等第三方无人机平台接口
    - 大疆DJI SDK集成
    - 其他无人机厂商API对接
    - 实时数据流处理
    - 设备状态监控
    """

    def __init__(self):
        self.active_drones = {}  # 存储活跃的无人机数据
        self.flight_paths = {}   # 存储每架无人机的飞行轨迹状态
        self.battery_levels = {} # 存储每架无人机的电池电量状态

        # 基础起飞位置（机巢位置）
        self.base_locations = {
            'drone_001': {'lat': 39.9042, 'lng': 116.4074},  # 北京
            'drone_002': {'lat': 31.2304, 'lng': 121.4737},  # 上海
            'drone_003': {'lat': 22.3193, 'lng': 114.1694},  # 深圳
        }

        # 初始化飞行轨迹参数
        self._init_flight_paths()

    def _init_flight_paths(self):
        """初始化每架无人机的飞行轨迹参数"""
        for drone_id, base_loc in self.base_locations.items():
            self.flight_paths[drone_id] = {
                'current_lat': base_loc['lat'],
                'current_lng': base_loc['lng'],
                'current_altitude': random.uniform(80, 150),  # 飞行高度80-150米
                'heading': random.uniform(0, 360),  # 当前航向
                'speed': random.uniform(8, 20),     # 飞行速度8-20m/s
                'flight_time': 0,                   # 飞行时间（秒）
                'waypoint_target': self._generate_waypoint(base_loc),  # 目标航点
                'last_update': datetime.datetime.now()
            }

            # 初始化电池电量（飞行中应该在合理范围）
            self.battery_levels[drone_id] = {
                'drone_battery': random.uniform(60, 95),      # 无人机电池60-95%
                'controller_battery': random.uniform(70, 100), # 遥控器电池70-100%
                'discharge_rate': random.uniform(0.5, 1.2)    # 放电速率%/分钟
            }

    def _generate_waypoint(self, base_location: Dict) -> Dict:
        """生成目标航点"""
        # 在基础位置周围5公里范围内生成目标点
        lat_offset = random.uniform(-0.045, 0.045)  # 约5km范围
        lng_offset = random.uniform(-0.045, 0.045)
        return {
            'lat': base_location['lat'] + lat_offset,
            'lng': base_location['lng'] + lng_offset
        }
    
    def generate_flight_real_time_data(self, drone_id: str) -> Dict[str, Any]:
        """
        生成符合前端FlightRealTimeData接口的实时数据
        
        Args:
            drone_id: 无人机ID
            
        Returns:
            符合FlightRealTimeData接口的数据字典
        """
        base_location = self.base_locations.get(drone_id, self.base_locations['drone_001'])
        
        # 生成位置数据（模拟飞行轨迹）
        position_data = self._generate_position_data(drone_id, base_location)
        
        # 生成环境数据
        environment_data = self._generate_environment_data()
        
        # 生成机巢状态数据
        nest_status_data = self._generate_nest_status_data()

        # 生成电池数据（传入drone_id以支持电量递减）
        battery_data = self._generate_battery_data(drone_id)

        # 生成温度数据
        temperature_data = self._generate_temperature_data()

        # 生成飞行任务数据（传入drone_id以获取实际任务状态）
        mission_data = self._generate_mission_data(drone_id)

        # 生成导航数据（传入drone_id以获取实际航向）
        navigation_data = self._generate_navigation_data(drone_id)

        # 生成飞行参数数据（传入drone_id以获取实际高度和距离）
        parameters_data = self._generate_parameters_data(drone_id)
        
        flight_data = {
            'droneId': drone_id,
            'position': position_data,
            'environment': environment_data,
            'nestStatus': nest_status_data,
            'battery': battery_data,
            'temperature': temperature_data,
            'mission': mission_data,
            'navigation': navigation_data,
            'parameters': parameters_data,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
        # 更新活跃无人机数据
        self.active_drones[drone_id] = flight_data
        
        logger.debug(f"生成无人机 {drone_id} 实时数据")
        return flight_data
    
    def _generate_position_data(self, drone_id: str, base_location: Dict) -> Dict[str, Any]:
        """生成连续的飞行轨迹位置数据"""
        if drone_id not in self.flight_paths:
            self._init_flight_paths()

        flight_state = self.flight_paths[drone_id]
        now = datetime.datetime.now()
        time_delta = (now - flight_state['last_update']).total_seconds()

        # 计算朝向目标航点的移动
        target = flight_state['waypoint_target']
        current_lat = flight_state['current_lat']
        current_lng = flight_state['current_lng']

        # 计算到目标点的距离和方向
        lat_diff = target['lat'] - current_lat
        lng_diff = target['lng'] - current_lng
        distance_to_target = (lat_diff**2 + lng_diff**2)**0.5

        # 如果接近目标点，生成新的目标航点
        if distance_to_target < 0.002:  # 约200米范围内
            flight_state['waypoint_target'] = self._generate_waypoint(base_location)
            target = flight_state['waypoint_target']
            lat_diff = target['lat'] - current_lat
            lng_diff = target['lng'] - current_lng

        # 计算移动方向和距离
        if distance_to_target > 0:
            # 标准化方向向量
            direction_lat = lat_diff / distance_to_target
            direction_lng = lng_diff / distance_to_target

            # 根据速度和时间计算移动距离
            speed_ms = flight_state['speed']  # m/s
            move_distance_deg = (speed_ms * time_delta) / 111000  # 转换为度数（约111km/度）

            # 更新位置
            flight_state['current_lat'] += direction_lat * move_distance_deg
            flight_state['current_lng'] += direction_lng * move_distance_deg

            # 更新航向角
            flight_state['heading'] = (math.atan2(lng_diff, lat_diff) * 180 / math.pi + 360) % 360

        # 高度变化（小幅波动）
        altitude_change = random.uniform(-2, 2) * time_delta
        flight_state['current_altitude'] = max(50, min(200,
            flight_state['current_altitude'] + altitude_change))

        # 速度变化（小幅调整）
        speed_change = random.uniform(-1, 1) * time_delta
        flight_state['speed'] = max(5, min(25, flight_state['speed'] + speed_change))

        # 更新飞行时间
        flight_state['flight_time'] += time_delta
        flight_state['last_update'] = now

        # 计算距离基站的距离
        base_lat = base_location['lat']
        base_lng = base_location['lng']
        distance_from_base = ((flight_state['current_lat'] - base_lat)**2 +
                             (flight_state['current_lng'] - base_lng)**2)**0.5 * 111  # 转换为公里
        flight_state['distance_from_base'] = distance_from_base

        return {
            'longitude': flight_state['current_lng'],
            'latitude': flight_state['current_lat'],
            'altitude': round(flight_state['current_altitude'], 1),
            'groundSpeed': round(flight_state['speed'], 1),
            'verticalSpeed': round(altitude_change / max(time_delta, 0.1), 1),
            'heading': round(flight_state['heading'], 1),
            'timestamp': now.isoformat()
        }
    
    def _generate_environment_data(self) -> Dict[str, str]:
        """生成环境数据"""
        wind_levels = ['无风', '微风', '轻风', '和风', '强风']
        weather_conditions = ['晴朗', '多云', '阴天', '小雨']
        
        return {
            'windSpeed': f"{random.randint(1,2)}级",
            'windLevel': wind_levels[2],
            'weather': weather_conditions[2],
            'humidity': f"{random.randint(30, 50)}%",
            'temperature': f"{random.randint(25, 35)}°C"
        }
    
    def _generate_nest_status_data(self) -> Dict[str, Any]:
        """生成机巢状态数据（飞行状态优化）"""
        # 飞行中的无人机，机巢状态应该符合逻辑
        return {
            'droneStatus': '飞行中',  # 固定为飞行中
            'doorStatus': '关闭',     # 飞行时机巢门关闭
            'platformStatus': '正常', # 平台状态正常
            'centeringStatus': '未归中',  # 飞行中未归中
            'signals': {
                'wifi': random.choice([True, False]),      # WiFi信号可能有波动
                'battery': True,                           # 电池信号正常
                'satellite': random.uniform(0, 1) > 0.1   # 90%概率GPS信号正常
            }
        }
    
    def _generate_battery_data(self, drone_id: str) -> Dict[str, Any]:
        """生成电池数据（飞行状态优化，电量递减）"""
        if drone_id not in self.battery_levels:
            self._init_flight_paths()

        battery_state = self.battery_levels[drone_id]

        # 模拟电池放电（每次调用消耗一点电量）
        discharge_amount = battery_state['discharge_rate'] / 30  # 假设每2秒调用一次，30次约1分钟
        battery_state['drone_battery'] = max(10, battery_state['drone_battery'] - discharge_amount)
        battery_state['controller_battery'] = max(20, battery_state['controller_battery'] - discharge_amount * 0.3)

        # 如果电量过低，重置到合理范围（模拟更换电池或充电）
        if battery_state['drone_battery'] < 15:
            battery_state['drone_battery'] = random.uniform(80, 95)
            logger.info(f"无人机 {drone_id} 电池电量重置（模拟更换电池）")

        return {
            'chargingStatus': '放电中',  # 飞行中固定为放电状态
            'powerStatus': '已开机',    # 飞行中固定为开机状态
            'current': round(random.uniform(8.0, 15.0), 2),  # 飞行时电流较大
            'voltage': round(random.uniform(11.8, 12.4), 1), # 飞行时电压稳定
            'cycleCount': random.randint(50, 300),            # 充放电次数
            'controllerBattery': round(battery_state['controller_battery'], 1),
            'droneBattery': round(battery_state['drone_battery'], 1)
        }
    
    def _generate_temperature_data(self) -> Dict[str, Any]:
        """生成温度数据（飞行状态优化）"""
        return {
            'airConditionerStatus': '开机',  # 飞行过程中空调通常开启
            'cabinTemperature': round(random.uniform(18, 28), 1),  # 空调开启，温度控制在舒适范围
            'batteryTemperature': round(random.uniform(25, 40), 1)  # 飞行时电池温度稍高
        }
    
    def _generate_mission_data(self, drone_id: str) -> Dict[str, Any]:
        """生成飞行任务数据（基于实际飞行状态）"""
        if drone_id not in self.flight_paths:
            self._init_flight_paths()

        flight_state = self.flight_paths[drone_id]

        # 根据无人机ID确定任务地点
        location_map = {
            'drone_001': '北京市区',
            'drone_002': '上海浦东',
            'drone_003': '深圳南山'
        }

        # 计算任务进度（基于飞行时间）
        flight_time_minutes = flight_state['flight_time'] / 60
        estimated_mission_time = 45  # 假设任务总时长45分钟
        progress = min(100, int((flight_time_minutes / estimated_mission_time) * 100))

        # 计算已飞行距离
        total_distance = flight_state['speed'] * flight_state['flight_time'] / 1000  # 转换为公里

        return {
            'progress': progress,
            'location': location_map.get(drone_id, '未知区域'),
            'duration': f"{int(flight_time_minutes)}分钟",
            'speed': round(flight_state['speed'], 1),
            'dataSync': '正常' if random.uniform(0, 1) > 0.05 else '异常',  # 95%概率正常
            'waypointCount': random.randint(8, 25),
            'estimatedPhotos': random.randint(50, 150),
            'distance': f"{round(total_distance, 1)}km",
            'altitude': f"{int(flight_state['current_altitude'])}m"
        }
    
    def _generate_navigation_data(self, drone_id: str) -> Dict[str, Any]:
        """生成导航数据（使用实际飞行状态）"""
        if drone_id not in self.flight_paths:
            self._init_flight_paths()

        flight_state = self.flight_paths[drone_id]

        return {
            'heading': round(flight_state['heading'], 1),  # 使用实际航向
            'gimbalAngle': round(random.uniform(-30, 30), 1),  # 云台角度小幅调整
            'horizontalSpeed': round(flight_state['speed'], 1),  # 使用实际水平速度
            'verticalSpeed': round(random.uniform(-2, 2), 1)     # 垂直速度小幅波动
        }
    
    def _generate_parameters_data(self, drone_id: str) -> Dict[str, Any]:
        """生成飞行参数数据（飞行状态优化）"""
        if drone_id not in self.flight_paths:
            self._init_flight_paths()

        flight_state = self.flight_paths[drone_id]

        # 飞行模式：主要是自动模式，偶尔手动模式
        flight_mode = '自动' if random.uniform(0, 1) > 0.2 else '手动'

        # 信号强度：飞行中信号可能有波动，但总体良好
        upload_signal_strength = random.randint(45, 70)
        download_signal_strength = random.randint(40, 65)

        # GPS卫星数量：飞行中应该有足够的卫星
        gps_count = random.randint(12, 20)

        # 视频信号：根据距离和信号强度决定
        distance = flight_state.get('distance_from_base', random.uniform(0.5, 3.0))
        if distance < 1.0:
            video_signal = '良好'
        elif distance < 2.5:
            video_signal = random.choice(['良好', '一般'])
        else:
            video_signal = random.choice(['一般', '差'])

        return {
            'groundAltitude': round(flight_state['current_altitude'], 1),
            'droneAltitude': round(flight_state['current_altitude'] - random.uniform(0, 5), 1),
            'droneDistance': round(distance, 2),
            'uploadSignal': f"-{upload_signal_strength}dBm",
            'downloadSignal': f"-{download_signal_strength}dBm",
            'gpsCount': gps_count,
            'flightMode': flight_mode,
            'compassStatus': '正常' if random.uniform(0, 1) > 0.05 else '异常',  # 95%概率正常
            'channelInterference': random.choice(['无', '轻微']) if random.uniform(0, 1) > 0.1 else '严重',
            'videoSignal': video_signal
        }
    
    def get_drone_status(self, drone_id: str) -> Optional[Dict[str, Any]]:
        """
        获取特定无人机的状态信息
        
        Args:
            drone_id: 无人机ID
            
        Returns:
            无人机状态数据，如果不存在则返回None
        """
        if drone_id in self.active_drones:
            return self.active_drones[drone_id]
        
        # 如果没有缓存数据，生成新的数据
        return self.generate_flight_real_time_data(drone_id)
    
    def get_all_active_drones(self) -> List[str]:
        """获取所有活跃的无人机ID列表"""
        return list(self.active_drones.keys())
    
    def remove_drone(self, drone_id: str) -> bool:
        """
        移除无人机数据
        
        Args:
            drone_id: 无人机ID
            
        Returns:
            是否成功移除
        """
        if drone_id in self.active_drones:
            del self.active_drones[drone_id]
            logger.info(f"移除无人机 {drone_id} 的数据")
            return True
        return False


# 全局无人机数据服务实例
drone_service = DroneDataService()
