# Generated by Django 4.2.2 on 2025-08-26 15:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my_app', '0003_ttdronebasicinfo_api_token_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='ttdronealerts',
            name='alert_geojson',
            field=models.TextField(blank=True, db_comment='预警地理信息（GeoJSON格式）', null=True),
        ),
        migrations.AddField(
            model_name='ttdronealerts',
            name='alert_parent_type',
            field=models.IntegerField(choices=[(1, '交通安全'), (2, '农林水利'), (3, '安全生产'), (4, '宣传广告'), (5, '消防安全'), (6, '市容环境'), (7, '治安隐患'), (8, '生态环境'), (9, '自然灾害'), (99, '其他')], db_comment='预警父类类型', db_index=True, default=99),
        ),
        migrations.AlterField(
            model_name='ttdronealerts',
            name='alert_type_id',
            field=models.IntegerField(choices=[(10, '道路损坏'), (11, '车辆违停'), (12, '交通拥堵'), (13, '占道经营'), (14, '设施损坏'), (15, '路灯故障'), (16, '井盖缺失'), (20, '松材线虫病'), (21, '病虫害'), (22, '森林火灾'), (23, '水域污染'), (24, '河道漂浮物'), (30, '安全隐患'), (31, '危险品'), (32, '建筑工地'), (33, '工地未苫盖'), (34, '在建农房'), (50, '消防通道堵塞'), (60, '暴露垃圾'), (61, '违章建筑'), (62, '流动摊贩'), (63, '积存建筑垃圾'), (64, '垃圾堆放'), (80, '异常聚集'), (90, '噪音污染'), (91, '光污染'), (92, '大气污染'), (93, '土壤污染'), (110, '其他环境问题'), (111, '其他安全问题'), (119, '其他')], db_comment='具体预警类型', db_index=True),
        ),
    ]
