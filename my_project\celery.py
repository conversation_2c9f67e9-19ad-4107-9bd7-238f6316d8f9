# celery.py

from __future__ import absolute_import, unicode_literals
import os
from celery import Celery

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'my_project.settings')

app = Celery('my_project')

app.config_from_object('django.conf:settings', namespace='CELERY')

# # 设置日志级别
# app.conf.update(
#     worker_hijack_root_logger=False,
#     worker_log_color=False,
#     worker_log_format=None,
#     worker_task_log_format=None,
#     worker_task_serializer='json',
#     worker_accept_content=['json'],
#     worker_log_level='INFO',  # 设置为'INFO'
# )

app.autodiscover_tasks()