#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time : 2024-12-26 当前系统时间
# <AUTHOR> zhous
# @Email : <EMAIL>
# @File : task_serializers.py
# @Descr : 后台管理任务序列化器
# @Software: VSCode

from rest_framework import serializers
from my_app.models import TtDroneTasks

class TaskAdminSerializer(serializers.ModelSerializer):
    """任务管理序列化器 - 后台管理专用"""

    class Meta:
        model = TtDroneTasks
        exclude = ['created_by_id', 'updated_by_id', 'reviewer_id']

class TaskReviewSerializer(serializers.Serializer):
    """任务审核序列化器"""
    review_status = serializers.ChoiceField(
        choices=[('approved', '通过'), ('rejected', '拒绝')],
        help_text="审核状态"
    )
    review_notes = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True,
        help_text="审核意见"
    )
    
    def validate_review_notes(self, value):
        """验证审核意见"""
        if self.initial_data.get('review_status') == 'rejected' and not value.strip():
            raise serializers.ValidationError("拒绝审核时必须填写审核意见")
        return value.strip() if value else ''
