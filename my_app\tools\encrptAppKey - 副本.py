"""
#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
@Project :django初始化框架
@File    :encrptAppKey.py
@IDE     :PyCharm
<AUTHOR>
@Date    :2024/4/28 18:12
@Descr:  对代码中引用的各种appkey进行加解密
"""
import base64
import re

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from vgis_encrption.encrptionTools import FernetEncryption, RSAEncryption

from Crypto.Random import get_random_bytes


# 从配置文件中
# key1 = ENCRPTION["key1"]
# key2 = ENCRPTION["key2"]
# key3 = ENCRPTION["key3"]
# key4 = ENCRPTION["key4"]

# 将明文解码成字符串
####################################新的
def AES_de(key, encrypted_str):
    if encrypted_str is None or encrypted_str == "":
        return encrypted_str
    # 解码base64字符串
    combined = base64.b64decode(encrypted_str)

    # 提取IV和密文
    iv = combined[:AES.block_size]
    encrypted_bytes = combined[AES.block_size:]

    # 创建解密对象
    cipher = AES.new(key.encode("utf-8"), AES.MODE_CBC, iv)

    # 执行解密
    decrypted_padded = cipher.decrypt(encrypted_bytes)

    # 去除填充
    from Crypto.Util.Padding import unpad
    decrypted_bytes = unpad(decrypted_padded, AES.block_size)

    # 转换为字符串返回
    return decrypted_bytes.decode("utf-8")
def AES_en(key, data):
    # 将数据转换为字节并补齐到16字节倍数
    data_bytes = data.encode("utf-8")
    padded_data = pad(data_bytes, AES.block_size)

    # 生成随机IV
    iv = get_random_bytes(AES.block_size)

    # 创建加密对象，使用CBC模式
    cipher = AES.new(key.encode("utf-8"), AES.MODE_CBC, iv)

    # 执行加密
    encrypted_bytes = cipher.encrypt(padded_data)

    # 将IV和密文组合并用base64编码
    combined = iv + encrypted_bytes
    encoded = base64.b64encode(combined)

    # 转换为字符串返回
    return encoded.decode("utf-8")

# 或者从generatorSetKey.py中生成key读取
key1 = 'miyaovgis0704gis'




app_key = "zhous"
# print("aes_key------------>{}",aes_key)
print("app_key----->{}".format(app_key))
encrpt_app_key = AES_en(key1, app_key)
print("encrpt_app_key----->{}".format(encrpt_app_key))
descpt_app_key = AES_de(key1, "knlXv8OwAJrWPx16024IkMG9RUEG1tYLZ8yfGQvdotg=")
print("descpt_app_key----->{}".format(descpt_app_key))
