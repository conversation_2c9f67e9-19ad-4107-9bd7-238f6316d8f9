#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time    : 2025-08-26 14:00:00
# <AUTHOR> zhous
# @Email   : <EMAIL>
# @File    : base_views.py
# @Descr   : 安全基础ViewSet类
# @Software: VSCode

from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.db.models import Q

from my_project.token import ExpiringTokenAuthentication


class SecureModelViewSet(viewsets.ModelViewSet):
    """
    安全的基础ViewSet，自动处理用户ID
    所有业务ViewSet都应该继承此类
    """
    permission_classes = (IsAuthenticated,)
    authentication_classes = (ExpiringTokenAuthentication,)

    def perform_create(self, serializer):
        """创建时自动设置创建用户"""
        serializer.save(
            created_by_id=self.request.user.id,
            create_time=timezone.now()
        )

    def perform_update(self, serializer):
        """更新时自动设置更新用户"""
        serializer.save(
            updated_by_id=self.request.user.id,
            update_time=timezone.now()
        )


class SecureTaskViewSet(SecureModelViewSet):
    """
    任务专用的安全ViewSet，处理审核相关逻辑
    """

    def perform_create(self, serializer):
        """创建任务时设置创建用户"""
        super().perform_create(serializer)

    def perform_update(self, serializer):
        """更新任务时处理审核逻辑"""
        # 如果是审核操作，设置审核人
        if 'review_status' in serializer.validated_data:
            serializer.save(
                updated_by_id=self.request.user.id,
                update_time=timezone.now(),
                reviewer_id=self.request.user.id,
                review_time=timezone.now()
            )
        else:
            super().perform_update(serializer)



