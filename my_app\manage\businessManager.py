#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
# @Time    :  2023/3/13 15:31
# <AUTHOR> chenxw
# @Email   : <EMAIL>
# @File    : businessManager.py
# @Descr   : 
# @Software: PyCharm
import json
import logging
import math

from django.http import QueryDict
from vgis_log.logTools import Lo<PERSON>Helper
from itertools import groupby
from my_app.manage.commonManager import CommonOperator
from my_app.serializers import TtViewBookmarkDataSerializer, TtFeatureAnnotationDataSerializer
from my_app.utils.businessUtility import businessHelper
from my_app.utils.commonUtility import CommonHelper
from my_app.utils.databaseUtility import DatabaseHelper
from my_app.views.response.baseRespone import Result
from my_project import settings
from my_project.customPageNumberPagination import CustomPageNumberPagination
from rest_framework.response import Response
from geopy.distance import distance
from geopy.point import Point

logger = logging.getLogger('django')
from my_app.models import SysLog, TtAnnotationSysmbolData, TmBingtuan, TtFeatureAnnotationeNewFields, \
    TtFeatureAnnotationData


class BuisnessOperator:
    def __init__(self, connection):
        self.connection = connection

    # 查询方向书签数据列表
    def query_view_bookmark_data_list(self, request):
        """
        查询视图书签数据列表，支持分页查询。

        :param request: 请求对象，包含分页参数 page 和 size
        :return: 包含分页数据的响应对象，若参数错误或无数据则返回相应错误信息
        """
        # 从请求数据中获取分页参数
        data = request.data
        page = data.get('page')
        size = data.get('size')

        # 检查分页参数是否缺失
        if page is None or size is None:
            msg = "page 和size 都是必传参数"
            json_data = json.dumps(msg)
            return Result.fail(msg, json_data)
        # 检查 size 是否超过最大限制
        if size > settings.PAGE_MAX_SIZE:
            msg = "size 不可以大于" + str(settings.PAGE_MAX_SIZE)
            json_data = json.dumps(msg)
            return Result.fail(msg, json_data)
        # 检查 page 和 size 是否为 0
        if page == 0 or size == 0:
            msg = "page 或者size 不可以等于0 "
            json_data = json.dumps(msg)
            return Result.fail(msg, json_data)

        # 调用业务助手类获取查询结果
        results = businessHelper.get_view_bookmark_data_query_result(data, request)

        if results:
            # 获取结果总数
            page_count = results.count()
            # 创建自定义分页器对象
            customPageNumberPagination = CustomPageNumberPagination()
            # 设置分页器的当前页码
            customPageNumberPagination.page = page
            # 设置分页器的每页大小
            customPageNumberPagination.page_size = size
            # 创建可修改的 QueryDict 对象
            new_query_params = QueryDict(mutable=True)
            # 更新 QueryDict 中的页码参数
            new_query_params.update({'page': page})
            # 将新的 QueryDict 赋值给 request 的 GET 属性
            request._request.GET = new_query_params
            # 对查询结果进行分页
            r_page = customPageNumberPagination.paginate_queryset(results, request, self)
            # 对当前页的数据进行序列化
            articles_serializer = TtViewBookmarkDataSerializer(r_page, many=True)

            # 过滤掉不需要的字段，并增加一些字段
            for d in articles_serializer.data:
                # 移除不需要的字段
                d.pop('lng')
                d.pop('lat')
                d.pop('elv')
                d.pop('heading')
                d.pop('pitch')
                d.pop('roll')
                d.pop('remark')
                d.pop('create_user_id')
                d.pop('modify_user_id')
                # 处理时间格式
                d['create_time'] = d['create_time'].replace("T", " ") if d['create_time'] is not None else None
                d['modify_time'] = d['modify_time'].replace("T", " ") if d['modify_time'] is not None else None
                # 处理兵团代码列表
                bingtuan_code_list = d["bingtuan_code_list"].split(",")
                bingtuan_json_list = []
                for bingtuan_code in bingtuan_code_list:
                    bingtuan_json = "bingtuan/{}.geoJson".format(bingtuan_code)
                    bingtuan_json_list.append(bingtuan_json)
                bingtuan_json_list = ",".join(bingtuan_json_list)
                d['bingtuan_json_list'] = bingtuan_json_list

            # 封装分页数据并添加 URL 头
            res = Result.page_list(articles_serializer.data, page_count)
            res["json_url_head"] = CommonHelper.get_url_head()
            return Response(res)
        else:
            # 若没有查询结果，返回空的分页数据
            return Response(Result.page_list(0))

    # 查询标注数据列表
    def query_annotation_data_list(self, request):
        """
        查询标注数据列表，支持分页查询。

        :param request: 请求对象，包含分页参数 page 和 size
        :return: 包含分页数据的响应对象，若参数错误或无数据则返回相应错误信息
        """
        # 从请求数据中获取分页参数
        data = request.data
        page = data.get('page')
        size = data.get('size')

        # 检查分页参数是否缺失
        if page is None or size is None:
            msg = "page 和size 都是必传参数"
            json_data = json.dumps(msg)
            return Result.fail(msg, json_data)
        # 检查 size 是否超过最大限制
        if size > settings.PAGE_MAX_SIZE:
            msg = "size 不可以大于" + str(settings.PAGE_MAX_SIZE)
            json_data = json.dumps(msg)
            return Result.fail(msg, json_data)
        # 检查 page 和 size 是否为 0
        if page == 0 or size == 0:
            msg = "page 或者size 不可以等于0 "
            json_data = json.dumps(msg)
            return Result.fail(msg, json_data)

        # 调用业务助手类获取查询结果
        results = businessHelper.get_annotation_data_query_result(data, request)

        if results:
            # 获取结果总数
            page_count = results.count()
            # 创建自定义分页器对象
            customPageNumberPagination = CustomPageNumberPagination()
            # 设置分页器的当前页码
            customPageNumberPagination.page = page
            # 设置分页器的每页大小
            customPageNumberPagination.page_size = size
            # 创建可修改的 QueryDict 对象
            new_query_params = QueryDict(mutable=True)
            # 更新 QueryDict 中的页码参数
            new_query_params.update({'page': page})
            # 将新的 QueryDict 赋值给 request 的 GET 属性
            request._request.GET = new_query_params
            # 对查询结果进行分页
            r_page = customPageNumberPagination.paginate_queryset(results, request, self)
            # 对当前页的数据进行序列化
            articles_serializer = TtFeatureAnnotationDataSerializer(r_page, many=True)

            # 过滤掉不需要的字段，并增加一些字段
            for d in articles_serializer.data:
                # 移除不需要的字段
                d.pop('create_user_id')
                d.pop('modify_user_id')
                # 处理时间格式
                d['create_time'] = d['create_time'].replace("T", " ") if d['create_time'] is not None else None
                d['modify_time'] = d['modify_time'].replace("T", " ") if d['modify_time'] is not None else None
                # 获取标注符号路径和 TD 符号路径
                d['annotation_sysmbol_path'] = TtAnnotationSysmbolData.objects.get(
                    id=d['annotation_sysmbol_id']).sysmbol_path
                d['td_sysmbol_path'] = TtAnnotationSysmbolData.objects.get(
                    id=d['annotation_sysmbol_id']).td_sysmbol
                # 增加标注数据的新增字段信息
                if TtFeatureAnnotationeNewFields.objects.filter(annotation_id=d['id']).exists():
                    # 获取历史字段的英文名、中文名和类型列表
                    histroy_fields_enam_list = TtFeatureAnnotationeNewFields.objects.get(
                        annotation_id=d['id']).new_fields_ename.split(",")
                    histroy_fields_cname_list = TtFeatureAnnotationeNewFields.objects.get(
                        annotation_id=d['id']).new_fields_cname.split(",")
                    histroy_fields_type_list = TtFeatureAnnotationeNewFields.objects.get(
                        annotation_id=d['id']).new_fields_type.split(",")
                    history_fields_info = []
                    for i in range(len(histroy_fields_enam_list)):
                        # 构建 SQL 查询语句
                        tmpsql = "select id," + histroy_fields_enam_list[
                            i] + " from tt_feature_annotation_data where id=" + str(d['id'])
                        # 执行原始 SQL 查询
                        tmp_field_value = TtFeatureAnnotationData.objects.raw(tmpsql)
                        # 构建字段信息字典并添加到列表中
                        history_fields_info.append(
                            {"field_ename": histroy_fields_enam_list[i], "field_cname": histroy_fields_cname_list[i],
                             "field_type": histroy_fields_type_list[i],
                             "field_value": tmp_field_value[0].__dict__[histroy_fields_enam_list[i]]})
                    d['history_fields'] = history_fields_info

            # 封装分页数据并添加 URL 头
            res = Result.page_list(articles_serializer.data, page_count)
            res["url_head"] = CommonHelper.get_url_head()
            return Response(res)
        else:
            # 若没有查询结果，返回空的分页数据
            return Response(Result.page_list(0))

    def get_next_field(self, fields):
        """
        根据给定的字段列表，生成下一个增长的字段名。

        :param fields: 包含现有字段名的列表
        :return: 下一个增长的字段名，格式为 'field_xxxxx'
        """
        # 解析字段并提取数字部分
        numbers = []
        for field in fields:
            try:
                # 假设所有字段都以 'field_' 开头，后面跟着数字
                number_part = field.replace('field_', '')
                number = int(number_part)
                numbers.append(number)
            except ValueError:
                # 如果转换失败，跳过该字段
                continue

        if not numbers:
            # 如果没有有效的数字，返回默认的第一个值
            return "field_00001"

        # 找到最大数字并加1
        next_number = max(numbers) + 1

        # 格式化为带有前导零的字符串
        next_field = f"field_{next_number:05d}"

        return next_field

    # 示例用法
    # fields = ["field_00001", "field_00002", "field_00003"]
    # next_field = get_next_field(fields)
    # print(f"下一个增长的字符串是: {next_field}")

    # 获取标注数据表下个新增的字段英文名
    def get_annotation_data_add_field(self):
        """
        获取标注数据表下一个新增的字段英文名。

        :return: 下一个新增的字段英文名，格式为 'field_xxxxx'
        """
        # 构建 SQL 查询语句，查询以 'field_' 开头的字段名
        sql = "SELECT COLUMN_NAME as field_ename  FROM USER_COL_COMMENTS WHERE TABLE_NAME = 'tt_feature_annotation_data' and COLUMN_NAME LIKE 'field_%';"
        # 获取数据库游标
        cursor = self.connection.cursor()
        # 执行 SQL 查询
        cursor.execute(sql)
        # 获取查询结果
        records = cursor.fetchall()
        fields_list = []
        for record in records:
            fields_list.append(record[0])
        # 调用 get_next_field 方法生成下一个字段名
        return self.get_next_field(fields_list)

    # 判断标注数据新增字段中文名是否重复
    def is_annotation_data_add_field_cname_repeat(self, field_cname):
        """
        判断标注数据新增字段的中文名是否重复。

        :param field_cname: 要检查的字段中文名
        :return: 如果重复返回 True，否则返回 False
        """
        # 构建 SQL 查询语句，查询指定中文名的字段
        sql = "SELECT tablea.COLUMN_NAME,tablea.COMMENTS FROM USER_COL_COMMENTS tablea WHERE tablea.TABLE_NAME = 'tt_feature_annotation_data' and tablea.COMMENTS = '{}';".format(
            field_cname)
        # 获取数据库游标
        cursor = self.connection.cursor()
        # 执行 SQL 查询
        cursor.execute(sql)
        # 获取查询结果的第一行
        record = cursor.fetchone()
        if record is None:
            return False
        else:
            return True

    # 获取标注数据的字段类型
    def get_annotation_data_field_type(self, field_ename):
        """
        获取标注数据表中指定字段的自定义字段类型。

        :param field_ename: 要查询的字段英文名
        :return: 自定义字段类型
        """
        # 构建 SQL 查询语句，查询指定表中指定字段的列名和数据类型
        sql = "SELECT COLUMN_NAME,DATA_TYPE FROM  USER_TAB_COLUMNS WHERE TABLE_NAME = 'tt_feature_annotation_data' and COLUMN_NAME ='{}';".format(
            field_ename)
        # 获取数据库游标
        cursor = self.connection.cursor()
        # 执行 SQL 查询
        cursor.execute(sql)
        # 获取查询结果的第一行
        record = cursor.fetchone()
        # 调用业务助手的方法将数据库的数据类型转换为自定义字段类型
        return businessHelper.convert_to_custom_field_type(record[1])

    # 获取标注数据表所有历史新增字段（field_00001\field_00002...）
    def get_annotation_data_all_history_field(self):
        """
        获取标注数据表中所有以 'field_' 开头的历史新增字段的信息。

        :return: 包含字段信息的字典，包含成功标志和字段信息列表
        """
        # 构建 SQL 查询语句，查询标注数据表中以 'field_' 开头的字段的英文名、中文名和数据类型
        sql = "SELECT tablea.COLUMN_NAME as field_ename,tablea.COMMENTS as field_cname,tableb.DATA_TYPE as field_type FROM USER_COL_COMMENTS tablea,USER_TAB_COLUMNS tableb WHERE tablea.TABLE_NAME = 'tt_feature_annotation_data' and tableb.TABLE_NAME = 'tt_feature_annotation_data' and tablea.COLUMN_NAME=tableb.COLUMN_NAME and tablea.COLUMN_NAME LIKE 'field_%';"
        # 获取数据库游标
        cursor = self.connection.cursor()
        # 执行 SQL 查询
        cursor.execute(sql)
        # 获取查询结果
        records = cursor.fetchall()
        # 获取查询结果的字段名列表
        field_name_list = [description[0] for description in cursor.description]
        # 获取查询结果的字段类型列表
        field_type_list = [description[1] for description in cursor.description]
        data_list = []
        # 将查询字段名和查询结果两个列表拼接成字典，key 为字段名，value 为字段值
        for field_value_list in records:
            data_list.append(dict(list(zip(field_name_list, field_value_list))))
        # 将每个字段的数据类型转换为自定义字段类型
        for data in data_list:
            data["field_type"] = businessHelper.convert_to_custom_field_type(data["field_type"])
        # 构建包含成功标志和字段信息列表的字典
        res = {
            'success': True,
            'result': data_list
        }
        return res

    def add_table_prefix(self, columns, table_alias):
        """
        为输入的列名添加表别名前缀。

        :param columns: 以逗号分隔的列名字符串
        :param table_alias: 表别名
        :return: 带有表别名前缀的列名字符串，列名之间用逗号分隔
        """
        # 将输入字符串按逗号分割成单独的列名，并去除可能存在的空白
        column_list = [col.strip() for col in columns.split(',')]

        # 为每个列名添加表别名和点符号作为前缀
        prefixed_columns = [f"{table_alias}.{col}" for col in column_list]

        # 将所有带前缀的列名连接成一个字符串，列名之间用逗号分隔
        return ', '.join(prefixed_columns)

    # 计算外接矩形的坐标
    def calculate_bounding_box(self, circle_center, circle_radius):
        """
        根据圆的中心点和半径计算其外接矩形的坐标。

        :param circle_center: 圆的中心点坐标，格式为 [经度, 纬度]
        :param circle_radius: 圆的半径（米）
        :return: 外接矩形的坐标列表，格式为 [[西经, 北纬], [东经, 北纬], [东经, 南纬], [西经, 南纬], [西经, 北纬]]
        """
        # 创建中心点对象
        center_point = Point(circle_center[1], circle_center[0])

        # 计算四个方向的距离
        north = distance(meters=circle_radius).destination(center_point, bearing=0)
        south = distance(meters=circle_radius).destination(center_point, bearing=180)
        east = distance(meters=circle_radius).destination(center_point, bearing=90)
        west = distance(meters=circle_radius).destination(center_point, bearing=270)

        # 获取外接矩形的坐标
        bounding_box = [
            [west.longitude, north.latitude],
            [east.longitude, north.latitude],
            [east.longitude, south.latitude],
            [west.longitude, south.latitude],
            [west.longitude, north.latitude]
        ]
        return bounding_box

    def get_table_list(self, data_table_name, queryword, remove_fields, connection, **kwargs):
        """
        根据传入的参数查询数据表，并对查询结果进行处理和分组。

        :param data_table_name: 要查询的数据表名
        :param queryword: 查询关键词
        :param remove_fields: 要移除的字段列表
        :param connection: 数据库连接对象
        :param kwargs: 可选参数，包含区划代码、区划类型、查询区域、采集时间范围、标注点类型、敌我信息、情报来源、分组字段、用户 ID 等
        :return: 分组后的查询结果列表和字段信息列表
        """
        # 从 kwargs 中获取可选参数的值
        division_code = None
        if "division_code" in kwargs:
            division_code = kwargs["division_code"]
        division_type = None
        if "division_type" in kwargs:
            division_type = kwargs["division_type"]
        query_region = None
        if "query_region" in kwargs:
            query_region = kwargs["query_region"]
        collect_start_time = None
        if "collect_start_time" in kwargs:
            collect_start_time = kwargs["collect_start_time"]
        collect_end_time = None
        if "collect_end_time" in kwargs:
            collect_end_time = kwargs["collect_end_time"]
        point_type = None
        if "point_type" in kwargs:
            point_type = kwargs["point_type"]
        friend_foe_info = None
        if "friend_foe_info" in kwargs:
            friend_foe_info = kwargs["friend_foe_info"]
        intelligence_source = None
        if "intelligence_source" in kwargs:
            intelligence_source = kwargs["intelligence_source"]
        if "groupby_field" in kwargs:
            groupby_field = kwargs["groupby_field"]
        if "user_id" in kwargs:
            user_id = kwargs["user_id"]

        # 获取数据表中的字符串类型字段
        str_fields = DatabaseHelper.get_string_field_of_table(data_table_name, connection)
        # 获取数据表中的非几何类型字段
        non_geom_fields = DatabaseHelper.get_non_geom_field_of_table(data_table_name, connection)

        # 区划必须有，默认为全疆
        if division_type == "行政区划":
            division_table_name = "WJYY_GZ_TM_DISTRICT"
            if division_code is None:
                division_code = 650000
        elif division_type == "兵团区划":
            division_table_name = "tm_bingtuan"
            if division_code is None:
                division_code = 65000000

        # 表别名
        table_alias = "p"
        # 为非几何字段添加表别名前缀
        query_fields_alias = self.add_table_prefix(",".join(non_geom_fields), table_alias)
        # 构建初始的 SQL 查询语句
        search_sql = "select {}  from {} p ,{} b  where 1=1 ".format(query_fields_alias, data_table_name,
                                                                     division_table_name)
        # 区划匹配
        if division_code is not None and str(division_code).strip() != "":
            search_sql += " and b.dis_code = {} and dmgeo.ST_Contains(b.geom, p.geom) = 1 ".format(division_code)
        # 空间查询
        if query_region is not None and str(query_region).strip() != "":
            if query_region["region_type"] == "rect" or query_region["region_type"] == "polygon":
                coordinates = query_region["region_points"]
                # 将坐标转换为所需格式
                formatted_coordinates = ", ".join([f"{lon} {lat}" for lon, lat in coordinates])
                # 添加空间查询条件
                search_sql += " and dmgeo.ST_Contains(dmgeo.ST_PolyFromText('polygon(({}))',4326), p.geom)=1".format(
                    formatted_coordinates)
            if query_region["region_type"] == "circle":
                circle_center = query_region["region_params"]["cirlce_center"]
                circle_radius = query_region["region_params"]["circle_radius"]
                # 计算圆的外接矩形坐标
                coordinates = self.calculate_bounding_box(circle_center, circle_radius)
                # 将坐标转换为所需格式
                formatted_coordinates = ", ".join([f"{lon} {lat}" for lon, lat in coordinates])
                # 添加空间查询条件
                search_sql += " and dmgeo.ST_Contains( dmgeo.ST_CreateCircle(dmgeo.ST_PolyFromText('polygon(({}))',4326),{}), p.geom)=1".format(
                    formatted_coordinates, 300)

        # 添加几何字段不为空的条件
        search_sql += " and b.geom is not null"
        search_sql += " and p.geom is not null"
        # 采集时间条件
        if collect_start_time is not None and str(collect_start_time).strip() != "":
            search_sql += " and p.create_time >='{}' ".format(collect_start_time)
        if collect_end_time is not None and str(collect_end_time).strip() != "":
            search_sql += " and p.create_time <='{}' ".format(collect_end_time)

        # 标注点类型条件
        if point_type is not None and str(point_type).strip() != "":
            search_sql += " and p.point_type in {} ".format('({})'.format(','.join(str(num) for num in point_type)))

        # 敌我信息条件
        if friend_foe_info is not None and str(friend_foe_info).strip() != "":
            search_sql += " and p.friend_foe_information in {} ".format(
                '({})'.format(','.join(str(num) for num in friend_foe_info)))
        # 情报来源条件
        if intelligence_source is not None and str(intelligence_source).strip() != "":
            search_sql += " and p.intelligence_source in {} ".format(
                '({})'.format(','.join(str(num) for num in intelligence_source)))

        # 标注数据查询，只能查询自己的
        if data_table_name == "tt_feature_annotation_data":
            search_sql += " and p.create_user_id=" + str(user_id)

        # 关键词模糊匹配
        if queryword is not None and str(queryword).strip() != "":
            search_sql += " and ("
            for str_field in str_fields:
                search_sql += " p.{} like '%{}%' ".format(str_field, queryword)
                search_sql += " or "
            search_sql = search_sql.rstrip(" or ")
            search_sql += ") "

        # 打印最终的 SQL 查询语句
        print(search_sql)
        # 获取数据库游标
        cursor = connection.cursor()
        # 执行 SQL 查询
        cursor.execute(search_sql)
        # 获取查询结果
        records = cursor.fetchall()
        # 获取查询结果的字段名称
        field_name_list = [description[0] for description in cursor.description]
        # 获取查询结果的字段类型
        field_type_list = [description[1] for description in cursor.description]
        data_list = []
        # 将查询字段名和查询结果两个列表拼接成字典，key 为字段名，value 为字段值
        for field_value_list in records:
            data_list.append(dict(list(zip(field_name_list, field_value_list))))
        # 格式化数据列表
        CommonOperator.format_data_list(data_list, field_type_list)

        # 首先根据分组字段排序
        data_list.sort(key=lambda x: x[groupby_field])

        # 使用 groupby 进行分组
        grouped_data = []
        for key, group in groupby(data_list, lambda x: x[groupby_field]):
            grouped_data.append(list(group))

        all_data_list = []
        # 打印分组后的结果
        for group in grouped_data:
            all_data_list.append({"type": group[0][groupby_field], "records": group})

        # 只保留要显示的字段
        CommonOperator.remove_field_of_data_list(data_table_name, remove_fields, data_list)
        # 获取字段信息列表
        field_info_list = CommonOperator.get_field_info_of_table(data_table_name, remove_fields, connection)
        return all_data_list, field_info_list

    # 全文检索
    def full_search(self, request, function_title, connection):
        """
        进行全量搜索，对 `tt_poi_data` 和 `tt_feature_annotation_data` 表进行查询，并返回查询结果。

        :param request: 请求对象，包含查询关键词、区划类型、区划代码等信息
        :param function_title: 功能标题，用于日志记录
        :param connection: 数据库连接对象
        :return: 包含查询结果的字典，若出现异常则返回异常处理后的结果
        """
        # 获取请求的 API 路径
        api_path = request.path
        # 从请求数据中获取查询关键词
        queryword = request.data["queryword"]
        # 从请求数据中获取区划类型
        division_type = request.data["division_type"]
        # 从请求数据中获取区划代码
        division_code = request.data["division_code"]
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        try:
            # 定义要查询的表名
            table_name = "tt_poi_data"
            # 定义要移除的字段列表
            remove_fields = ["id", "create_time", "modify_time", "create_user_id", "modify_user_id"]
            # 调用 get_table_list 方法进行查询，按 poi_type 分组
            poi_data_list, poi_data_field_info_list = self.get_table_list(table_name, queryword, remove_fields,
                                                                          connection,
                                                                          groupby_field="poi_type",
                                                                          query_method="full_search",
                                                                          division_code=division_code,
                                                                          division_type=division_type
                                                                          )
            # 定义要查询的表名
            table_name = "tt_feature_annotation_data"
            # 定义要移除的字段列表
            remove_fields = ["id", "create_time", "modify_time", "create_user_id", "modify_user_id", "type",
                             "friend_foe_information", "intelligence_source", "point_type"]
            # 调用 get_table_list 方法进行查询，按 point_type 分组，并传入用户 ID
            anno_data_list, anno_data_field_info_list = self.get_table_list(table_name, queryword, remove_fields,
                                                                            connection,
                                                                            groupby_field="point_type",
                                                                            query_method="full_search",
                                                                            division_code=division_code,
                                                                            division_type=division_type,
                                                                            user_id=request.auth.user_id)

            # 构建包含查询结果的字典
            res = {
                'success': True,
                'poi_search': {
                    # 'count': len(poi_data_list),
                    'fields': poi_data_field_info_list,
                    'result': poi_data_list
                },
                'annotation_search': {
                    # 'count': len(anno_data_list),
                    'fields': anno_data_field_info_list,
                    'result': anno_data_list
                }
            }
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            return res

        except Exception as exp:
            # 若出现异常，记录异常日志并返回异常处理结果
            res = LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                             request.auth.user, request,
                                                             function_title, None, exp)
            return res

    # 高级检索
    def advance_search(self, request, function_title, connection):
        """
        进行高级检索，根据查询类型对 `tt_poi_data` 或 `tt_feature_annotation_data` 表进行查询，并返回查询结果。

        :param request: 请求对象，包含查询关键词、查询类型、查询区域、区划类型、区划代码、采集时间范围等信息
        :param function_title: 功能标题，用于日志记录
        :param connection: 数据库连接对象
        :return: 包含查询结果的字典，若出现异常则返回异常处理后的结果
        """
        # 获取请求的 API 路径
        api_path = request.path
        # 从请求数据中获取查询关键词
        queryword = request.data["queryword"]
        # 从请求数据中获取查询类型
        query_type = request.data["query_type"]
        # 从请求数据中获取查询区域
        query_region = request.data["query_region"]
        # 从请求数据中获取区划类型
        division_type = request.data["division_type"]
        # 从请求数据中获取区划代码
        division_code = request.data["division_code"]
        # 从请求数据中获取采集开始时间
        collect_start_time = request.data["collect_start_time"]
        # 从请求数据中获取采集结束时间
        collect_end_time = request.data["collect_end_time"]
        if query_type == "annotation":
            # 若查询类型为 annotation，获取标注点类型、敌我信息、情报来源
            point_type = request.data["point_type"]
            friend_foe_info = request.data["friend_foe_info"]
            intelligence_source = request.data["intelligence_source"]
            # 定义要查询的表名
            table_name = "tt_feature_annotation_data"
            # 定义要移除的字段列表
            remove_fields = ["id", "create_time", "modify_time", "create_user_id", "modify_user_id", "type",
                             "friend_foe_information", "intelligence_source", "point_type"]
            # 定义分组字段
            groupby_field = "point_type"
        elif query_type == "poi":
            # 若查询类型为 poi，将标注点类型、敌我信息、情报来源置为 None
            point_type = None
            friend_foe_info = None
            intelligence_source = None
            # 定义要查询的表名
            table_name = "tt_poi_data"
            # 定义要移除的字段列表
            remove_fields = ["id", "create_time", "modify_time", "create_user_id", "modify_user_id"]
            # 定义分组字段
            groupby_field = "poi_type"
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        try:
            # 调用 get_table_list 方法进行查询，传入各种查询条件和分组字段
            data_list, field_info_list = self.get_table_list(table_name, queryword, remove_fields, connection,
                                                             groupby_field=groupby_field,
                                                             query_method="advance_query",
                                                             division_code=division_code,
                                                             division_type=division_type, query_region=query_region,
                                                             collect_start_time=collect_start_time,
                                                             collect_end_time=collect_end_time, point_type=point_type,
                                                             friend_foe_info=friend_foe_info,
                                                             intelligence_source=intelligence_source,
                                                             user_id=request.auth.user_id)
            # 构建包含查询结果的字典
            res = {
                'success': True,
                # 'count': len(data_list),
                'fields': field_info_list,
                'result': data_list
            }
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            return res
        except Exception as exp:
            # 若出现异常，记录异常日志并返回异常处理结果
            res = LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                             request.auth.user, request,
                                                             function_title, None, exp)
            return res

    # 标注符号查询
    def search_symbol(self, request, function_title, connection):
        """
        根据符号类型查询 `tt_annotation_sysmbol_data` 表中的标注符号数据，并返回查询结果。

        :param request: 请求对象，包含符号类型信息
        :param function_title: 功能标题，用于日志记录
        :param connection: 数据库连接对象
        :return: 包含查询结果的字典，若出现异常则返回异常处理后的结果
        """
        # 获取请求的 API 路径
        api_path = request.path
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        try:
            # 构建 SQL 查询语句，根据符号类型查询标注符号数据
            sql = "select *  from tt_annotation_sysmbol_data where type={}".format(
                request.data["symbol_type"])
            # 获取数据库游标
            cursor = connection.cursor()
            # 执行 SQL 查询
            cursor.execute(sql)
            # 获取查询结果
            records = cursor.fetchall()
            # 获取查询结果的字段名称
            field_name_list = [description[0] for description in cursor.description]
            # 获取查询结果的字段类型
            field_type_list = [description[1] for description in cursor.description]
            data_list = []
            # 将查询字段名和查询结果两个列表拼接成字典，key 为字段名，value 为字段值
            for field_value_list in records:
                data_list.append(dict(list(zip(field_name_list, field_value_list))))
            # 格式化数据列表
            CommonOperator.format_data_list(data_list, field_type_list)

            # 构建包含查询结果的字典
            res = {
                'success': True,
                'count': len(data_list),
                "url_head": CommonHelper.get_url_head(),
                'result': data_list
            }
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            return res

        except Exception as exp:
            # 若出现异常，记录异常日志并返回异常处理结果
            res = LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                             request.auth.user, request,
                                                             function_title, None, exp)
            return res

    # 根据类型获取 POI 数据
    def get_poi_data_by_type(self, request, function_title, connection):
        """
        根据 POI 类型查询 `tt_poi_data` 表中的 POI 数据，并返回查询结果。

        :param request: 请求对象，包含 POI 类型信息
        :param function_title: 功能标题，用于日志记录
        :param connection: 数据库连接对象
        :return: 包含查询结果的字典，若出现异常则返回异常处理后的结果
        """
        # 获取请求的 API 路径
        api_path = request.path
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        try:
            # 构建 SQL 查询语句，根据 POI 类型查询 POI 数据
            sql = "select lng,lat,name,address,province,city,area  from tt_poi_data where poi_type='{}'".format(
                request.data["poi_type"])
            # 获取数据库游标
            cursor = connection.cursor()
            # 执行 SQL 查询
            cursor.execute(sql)
            # 获取查询结果
            records = cursor.fetchall()
            # 获取查询结果的字段名称
            field_name_list = [description[0] for description in cursor.description]
            # 获取查询结果的字段类型
            field_type_list = [description[1] for description in cursor.description]
            data_list = []
            # 将查询字段名和查询结果两个列表拼接成字典，key 为字段名，value 为字段值
            for field_value_list in records:
                data_list.append(dict(list(zip(field_name_list, field_value_list))))
            # 格式化数据列表
            CommonOperator.format_data_list(data_list, field_type_list)

            # 构建包含查询结果的字典
            res = {
                'success': True,
                'count': len(data_list),
                # "url_head": CommonHelper.get_url_head(),
                'result': data_list
            }
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            return res

        except Exception as exp:
            # 若出现异常，记录异常日志并返回异常处理结果
            res = LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                             request.auth.user, request,
                                                             function_title, None, exp)
            return res

    # 根据类型获取标注数据
    def get_annotation_data_by_type(self, request, function_title, connection):
        api_path = request.path
        start = LoggerHelper.set_start_log_info(logger)
        try:
            # 没有新增字段的
            sql = "select id,lng,lat,elv,name,friend_foe_information_name,intelligence_source_name,type_name  from tt_feature_annotation_data  where point_type_name ='{}' and id not in (select annotation_id from tt_feature_annotatione_new_fields)".format(
                request.data["annotation_type"])
            cursor = connection.cursor()
            cursor.execute(sql)
            records = cursor.fetchall()
            # # 获取查询结果的字段名称和类型
            # cursor.description获取查询的字段名
            field_name_list = [description[0] for description in cursor.description]
            field_type_list = [description[1] for description in cursor.description]
            data_list = []
            # 将查询字段名和查询结果两个列表拼接成功字典，key为字段名，value为字段值
            for field_value_list in records:
                data_list.append(dict(list(zip(field_name_list, field_value_list))))

            # 有新增字段的
            sql2 = "select tablea.id, tableb.new_fields_ename,tableb.new_fields_cname from tt_feature_annotation_data tablea, tt_feature_annotatione_new_fields tableb where tablea.point_type_name ='{}' and tablea.id=tableb.annotation_id".format(
                request.data["annotation_type"])
            cursor.execute(sql2)
            records2 = cursor.fetchall()
            for record2 in records2:
                sql3 = "select id,lng,lat,elv,name,friend_foe_information_name,intelligence_source_name,type_name, {} from tt_feature_annotation_data where id={}".format(
                    record2[1], record2[0], records2[1], record2[2])
                cursor.execute(sql3)
                records3 = cursor.fetchall()
                for field_value_list in records3:
                    field_name_list = [description[0] for description in cursor.description]
                    field_name_list.append("new_fields_ename")
                    field_name_list.append("new_fields_cname")
                    field_value_list2 = list(field_value_list)
                    field_value_list2.append(record2[1])
                    field_value_list2.append(record2[2])
                    field_type_list = [description[1] for description in cursor.description]
                    data_list.append(dict(list(zip(field_name_list, field_value_list2))))

            # for data in data_list:
            #     if TtFeatureAnnotationeNewFields.objects.filter(annotation_id=data["id"]).exists():
            #         new_fields_ename = TtFeatureAnnotationeNewFields.objects.get(
            #             annotation_id=data["id"]).new_fields_ename
            #         new_fields_cname = TtFeatureAnnotationeNewFields.objects.get(
            #             annotation_id=data["id"]).new_fields_cname
            #         sql2 = "select {} from tt_feature_annotation_data where id={}".format(new_fields_ename,
            #                                                                               data["id"])
            #         cursor.execute(sql2)
            #         records2 = cursor.fetchone()
            #
            #         # for index in new_fields_ename.split(","):
            #         data[new_field_ename] = records2[0]
            #         # for new_field_ename in new_fields_ename.split(","):
            #         #     # data[new_field_ename] = getattr(TtFeatureAnnotationData.objects.get(id=data["id"]),
            #         #     #                                 new_fields_ename)
            #         #     sql2 = "select {} from tt_feature_annotation_data where id={}".format(new_field_ename,
            #         #                                                                           data["id"])
            #         #     cursor.execute(sql2)
            #         #     records2 = cursor.fetchone()
            #         #     data[new_field_ename] = records2[0]
            #         data["new_fields_ename"] = new_fields_ename.split(",")
            #         data["new_fields_cname"] = new_fields_cname.split(",")

            CommonOperator.format_data_list(data_list, field_type_list)

            res = {
                'success': True,
                'count': len(data_list),
                # "url_head": CommonHelper.get_url_head(),
                'result': data_list
            }
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
            return res

        except Exception as exp:
            res = LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                             request.auth.user, request,
                                                             function_title, None, exp)
            return res
