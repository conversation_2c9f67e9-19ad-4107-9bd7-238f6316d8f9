#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
# @Time    :  2022/12/15 20:13
# <AUTHOR> chenxw
# @Email   : <EMAIL>
# @File    : commonManager.py
# @Descr   : 多源数据
# @Software: PyCharm
import datetime
import logging
import os
import time
import uuid

from vgis_log.logTools import <PERSON><PERSON>Helper
from vgis_utils.vgis_http.httpTools import HttpHelper

from my_app import models
from my_app.models import SysLog, TtAnnotationSysmbolData, TtAnnotationType
from my_app.utils.commonUtility import <PERSON>flake<PERSON>U<PERSON>, CommonHelper
from my_project import settings
from my_project.settings import BASE_DIR, STATIC_NAME, APP_NAME

logger = logging.getLogger('django')


class CommonOperator:
    def __init__(self, connection):
        self.connection = connection

    @staticmethod
    def get_field_type_name_by_dm_value(type_code):
        """
        根据达梦数据库的类型代码获取对应的通用类型名称。

        :param type_code: 达梦数据库的类型代码，如 "class 'dmPython.BIGINT'>" 等
        :return: 对应的通用类型名称，如 "int"、"float" 等
        """
        # 默认类型为字符串
        type_name = "string"
        if type_code == "class 'dmPython.BIGINT'>" or type_code == "class 'dmPython.INTEGER'>" or type_code == "class 'dmPython.SMALLINT'>":
            # 整数类型
            type_name = "int"
        elif type_code == "class 'dmPython.FLOAT'>" or type_code == "class 'dmPython.DOUBLE'>":
            # 浮点类型
            type_name = "float"
        elif type_code == "class 'dmPython.DATETIME'>" or type_code == "class 'dmPython.TIMESTAMP'>":
            # 日期时间类型
            type_name = "datetime"
        elif type_code == "class 'dmPython.DATE'>":
            # 日期类型
            type_name = "date"
        elif type_code == "class 'dmPython.FIXED_STRING'>" or type_code == "<class 'dmPython.STRING'>":
            # 字符串类型
            type_name = "string"
        elif type_code == "class 'dmPython.BOOLEAN'>":
            # 布尔类型
            type_name = "bool"
        elif type_code == "class 'dmPython.JSON'>":
            # JSON 类型
            type_name = "jsonb"
        return type_name

    @staticmethod
    def get_field_type_name_by_code(type_code):
        """
        根据通用的类型代码获取对应的通用类型名称。

        :param type_code: 通用的类型代码，如 21、701 等
        :return: 对应的通用类型名称，如 "int"、"float" 等
        """
        # 默认类型为字符串
        type_name = "string"
        if type_code == 21 or type_code == 20 or type_code == 23:
            # 整数类型
            type_name = "int"
        elif type_code == 701:
            # 浮点类型
            type_name = "float"
        elif type_code == 1114 or type_code == 1184:
            # 日期时间类型
            type_name = "datetime"
        elif type_code == 1082:
            # 日期类型
            type_name = "date"
        elif type_code == 1043:
            # 字符串类型
            type_name = "string"
        elif type_code == 16:
            # 布尔类型
            type_name = "bool"
        elif type_code == 3802:
            # JSON 类型
            type_name = "jsonb"
        return type_name

    @staticmethod
    def format_data_list(data_list, field_type_list):
        """
        根据字段类型对数据列表中的数据进行格式化处理。

        :param data_list: 包含多个字典的列表，每个字典表示一条数据记录
        :param field_type_list: 字段类型列表，与数据列表中的字段一一对应
        """
        for data_dict in data_list:
            for field, field_type in zip(data_dict.keys(), field_type_list):
                # 根据达梦数据库类型代码获取通用类型名称
                field_type = CommonOperator.get_field_type_name_by_dm_value(field_type)
                if field_type == "int":
                    # 处理整数类型，若值不为空则转换为整数，否则置为空字符串
                    data_dict[field] = int(data_dict[field]) if data_dict[field] is not None else ""
                elif field_type == "float":
                    # 处理浮点类型，若值不为空则转换为浮点数，否则置为空字符串
                    data_dict[field] = float(data_dict[field]) if data_dict[field] is not None else ""
                elif field_type == "datetime":
                    # 处理日期时间类型，将日期时间字符串中的 'T' 替换为空格
                    data_dict[field] = str(data_dict[field]).replace("T", " ")
                elif field_type == "string":
                    # 处理字符串类型，若值不为空则转换为字符串，否则置为空字符串
                    data_dict[field] = str(data_dict[field]) if data_dict[field] is not None else ""
                elif field_type == "bool":
                    # 处理布尔类型，若值不为空则转换为布尔值，否则置为空字符串
                    data_dict[field] = bool(data_dict[field]) if data_dict[field] is not None else ""

    @staticmethod
    def remove_field_of_data_list(tablename, remove_fields, data_list):
        """
        从数据列表中移除指定的字段。

        :param tablename: 表名
        :param remove_fields: 要移除的字段列表
        :param data_list: 包含多个字典的列表，每个字典表示一条数据记录
        """
        # 从 data_list 中去掉不需要的字段
        for data_dict in data_list:
            for field in remove_fields:
                # 若字段存在则删除，不存在则忽略
                data_dict.pop(field, None)

    @staticmethod
    def get_field_info_of_table(tablename, remove_fields, connection):
        """
        获取指定表的字段信息，并移除指定的字段记录。

        :param tablename: 表名
        :param remove_fields: 要移除的字段列表
        :param connection: 数据库连接对象
        :return: 处理后的字段信息列表
        """
        # 构建 SQL 查询语句，查询指定表的字段英文名和中文名
        sql = '''
            SELECT COLUMN_NAME as field_ename, COMMENTS as field_cname
            FROM USER_COL_COMMENTS
            WHERE TABLE_NAME = '{}';
            '''.format(tablename)
        # 获取数据库游标
        cursor = connection.cursor()
        # 执行 SQL 查询
        cursor.execute(sql)
        # 获取查询结果
        records = cursor.fetchall()
        # 获取查询结果的字段名称
        field_name_list = [description[0] for description in cursor.description]
        # 获取查询结果的字段类型
        field_type_list = [description[1] for description in cursor.description]
        data_list = []
        # 将查询字段名和查询结果两个列表拼接成字典，key 为字段名，value 为字段值
        for field_value_list in records:
            data_list.append(dict(list(zip(field_name_list, field_value_list))))
        # 移除指定的字段记录
        return CommonOperator.remove_record_of_data_list(remove_fields, data_list)

    @staticmethod
    def remove_record_of_data_list(remove_fields, data_list):
        """
        从数据列表中移除字段英文名在指定移除字段列表中的记录。

        :param remove_fields: 要移除的字段英文名列表
        :param data_list: 包含多个字典的列表，每个字典表示一条字段信息记录
        :return: 处理后的字段信息列表
        """
        # 存储要移除的记录索引
        remove_index = []
        for index in range(len(data_list)):
            data_dict = data_list[index]
            if data_dict["field_ename"] in remove_fields:
                # 若字段英文名在移除字段列表中，则记录索引
                remove_index.append(index)
        for index in sorted(remove_index, reverse=True):
            # 按逆序删除记录，避免索引混乱
            del data_list[index]
        return data_list

    # 获取全国的分地区分省数据
    def get_region_and_province(self, request):
        """
        获取全国的分地区分省数据，包括地区名称、地区代码以及每个地区下的省份信息。

        :param request: 请求对象
        :return: 包含地区和省份信息的字典，若出现异常则返回包含错误信息的字典
        """
        res = ""
        # 记录开始时间
        start = time.perf_counter()
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 获取分区
            sql = "select distinct region_name,region_code from tm_region order by region_code"
            # 获取数据库游标
            cursor = self.connection.cursor()
            # 执行 SQL 查询
            cursor.execute(sql)
            # 获取查询结果
            records = cursor.fetchall()
            data_list = []
            for record in records:
                obj = {}
                # 地区名称
                obj['region_name'] = str(record[0])
                # 地区代码
                obj['region_code'] = int(record[1])
                # 查询该地区下的省份信息
                sql2 = "select dis_name,dis_code from tm_region where region_code={} order by id".format(
                    obj['region_code'])
                cursor.execute(sql2)
                records2 = cursor.fetchall()
                province_list = []
                for record2 in records2:
                    obj2 = {}
                    # 省份名称
                    obj2["province_name"] = str(record2[0])
                    # 省份代码
                    obj2["province_code"] = int(record2[1])
                    # 省份 JSON 文件的 URL
                    obj2['province_json'] = "http://{}:{}{}district/{}.json".format(settings.PROJECT_SERVICE_IP,
                                                                                    settings.PROJECT_SERVICE_PORT,
                                                                                    settings.STATIC_URL,
                                                                                    obj2['province_code'])
                    province_list.append(obj2)
                # 该地区下的省份列表
                obj['province_list'] = province_list
                data_list.append(obj)
            # 构建包含地区和省份信息的字典
            res = {
                'success': True,
                'total': len(data_list),
                'info': data_list
            }

            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录结束时间
            end = time.perf_counter()
            # 计算用时
            t = end - start
            logger.info("总共用时{}秒".format(t))

            # 插入日志信息
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "获取全国的分地区分省数据",
                                         "/api/tmDdistrict/getRegionAndProvince",
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            logger.error("获取全国的分地区分省数据失败：" + str(exp))
            logger.error(exp)
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])  # 发生异常所在的文件
            logger.error(exp.__traceback__.tb_lineno)  # 发生异常所在的行数
            # 构建包含错误信息的字典
            res = {
                'success': False,
                'info': "获取全国的分地区分省数据失败：{}".format(str(exp))
            }
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录结束时间
            end = time.perf_counter()
            # 计算用时
            t = end - start
            logger.info("总共用时{}秒".format(t))

            # 插入异常日志信息
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "获取全国的分地区分省数据失败",
                                         "/api/tmDdistrict/getRegionAndProvince",
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            return res

    # 获取兵团的师级数据
    def get_division_by_corps(self, corps_code, request):
        """
        根据新疆兵团的兵团代码获取对应的师级行政区划数据。

        :param corps_code: 新疆兵团的兵团代码
        :param request: 请求对象，包含请求的相关信息
        :return: 包含师级行政区划数据的字典，若成功则包含行政区划信息，若失败则包含错误信息
        """
        res = ""
        # 记录操作开始时间
        start = time.perf_counter()
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 构建 SQL 查询语句，从 tm_bingtuan 表中查询父代码为指定兵团代码的行政区划名称和代码，并按行政区划代码升序排序
            sql = "select dis_name,dis_code  from tm_bingtuan where parent_code={} order by dis_code asc ".format(
                corps_code)
            # 获取数据库游标
            cursor = self.connection.cursor()
            # 执行 SQL 查询
            cursor.execute(sql)
            # 获取查询结果
            records = cursor.fetchall()
            data_list = []
            for record in records:
                obj = {}
                # 行政区划名称
                obj['division_name'] = str(record[0])
                # 行政区划代码
                obj['division_code'] = int(record[1])
                # 行政区划对应的 GeoJSON 文件路径
                obj['division_json'] = "bingtuan/{}.geoJson".format(obj['division_code'])
                data_list.append(obj)
            # 构建包含成功信息和行政区划数据的字典
            res = {
                'success': True,
                'total': len(data_list),
                "url_head": CommonHelper.get_url_head(),
                'info': data_list
            }

            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            logger.info("总共用时{}秒".format(t))
            # 插入操作日志
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "获取新疆兵团师级数据",
                                         "/api/tmBingtuan/getDivisionByCorps",
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))

        except Exception as exp:
            # 记录错误信息，包括异常描述、异常对象、异常发生的文件和行号
            logger.error("获取新疆兵团师级数据失败：" + str(exp))
            logger.error(exp)
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])  # 发生异常所在的文件
            logger.error(exp.__traceback__.tb_lineno)  # 发生异常所在的行数
            # 构建包含失败信息和错误描述的字典
            res = {
                'success': False,
                'info': "获取新疆兵团师级数据失败：{}".format(str(exp))
            }
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            logger.info("总共用时{}秒".format(t))
            # 插入异常操作日志
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "获取新疆兵团师级数据失败",
                                         "/api/tmBingtuan/getDivisionByCorps",
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            return res

    # 根据师级获取团级数据
    def get_regiment_by_division(self, division_code, request):
        """
        根据新疆兵团的师级代码获取对应的团级行政区划数据。

        :param division_code: 新疆兵团的师级代码
        :param request: 请求对象，包含请求的相关信息
        :return: 包含团级行政区划数据的字典，若成功则包含行政区划信息，若失败则包含错误信息
        """
        res = ""
        # 记录操作开始时间
        start = time.perf_counter()
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 构建 SQL 查询语句，从 tm_bingtuan 表中查询父代码为指定师级代码的行政区划名称和代码
            sql = "select dis_name,dis_code  from tm_bingtuan where parent_code={}".format(division_code)
            # 获取数据库游标
            cursor = self.connection.cursor()
            # 执行 SQL 查询
            cursor.execute(sql)
            # 获取查询结果
            records = cursor.fetchall()
            data_list = []
            for record in records:
                obj = {}
                # 团级行政区划名称
                obj['regiment_name'] = str(record[0])
                # 团级行政区划代码
                obj['regiment_code'] = int(record[1])
                # 团级行政区划对应的 GeoJSON 文件路径
                obj['regiment_json'] = "bingtuan/{}.geoJson".format(obj['regiment_code'])
                data_list.append(obj)
            # 构建包含成功信息和行政区划数据的字典
            res = {
                'success': True,
                'total': len(data_list),
                "url_head": CommonHelper.get_url_head(),
                'info': data_list
            }

            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            logger.info("总共用时{}秒".format(t))
            # 插入操作日志
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "根据师级获取团级数据",
                                         "/api/tmBingtuan/getRegimentByDivision",
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录错误信息，包括异常描述、异常对象、异常发生的文件和行号
            logger.error("根据师级获取团级数据失败：" + str(exp))
            logger.error(exp)
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])  # 发生异常所在的文件
            logger.error(exp.__traceback__.tb_lineno)  # 发生异常所在的行数
            # 构建包含失败信息和错误描述的字典
            res = {
                'success': False,
                'info': "根据师级获取团级数据失败：{}".format(str(exp))
            }
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            logger.info("总共用时{}秒".format(t))
            # 插入异常操作日志
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "通过地市获取区县数据失败",
                                         "/api/tmBingtuan/getRegimentByDivision",
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            return res

    # 通过省份获取地市数据
    def get_city_by_province(self, province_code, request):
        """
        根据省份代码获取对应的地市行政区划数据。

        :param province_code: 省份代码
        :param request: 请求对象，包含请求的相关信息
        :return: 包含地市行政区划数据的字典，若成功则包含行政区划信息，若失败则包含错误信息
        """
        res = ""
        # 记录操作开始时间
        start = time.perf_counter()
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 构建 SQL 查询语句，从 WJYY_GZ_TM_DISTRICT 表中查询父代码为指定省份代码的行政区划名称和代码
            sql = 'select dis_name,dis_code  from "WJYY_GZ_TM_DISTRICT" where parent_code={}'.format(province_code)
            # 获取数据库游标
            cursor = self.connection.cursor()
            # 执行 SQL 查询
            cursor.execute(sql)
            # 获取查询结果
            records = cursor.fetchall()
            data_list = []
            for record in records:
                obj = {}
                # 地市行政区划名称
                obj['city_name'] = str(record[0])
                # 地市行政区划代码
                obj['city_code'] = int(record[1])
                # 地市行政区划对应的 GeoJSON 文件路径
                obj['city_json'] = "district/{}.geoJson".format(obj['city_code'])
                data_list.append(obj)
            # 构建包含成功信息和行政区划数据的字典
            res = {
                'success': True,
                'total': len(data_list),
                "url_head": CommonHelper.get_url_head(),
                'info': data_list
            }

            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            logger.info("总共用时{}秒".format(t))
            # 插入操作日志
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "通过省份获取地市数据",
                                         "/api/tmDdistrict/getCityByProvince",
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))

        except Exception as exp:
            # 记录错误信息，包括异常描述、异常对象、异常发生的文件和行号
            logger.error("通过省份获取地市数据失败：" + str(exp))
            logger.error(exp)
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])  # 发生异常所在的文件
            logger.error(exp.__traceback__.tb_lineno)  # 发生异常所在的行数
            # 构建包含失败信息和错误描述的字典
            res = {
                'success': False,
                'info': "通过省份获取地市数据失败：{}".format(str(exp))
            }
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            logger.info("总共用时{}秒".format(t))
            # 插入异常操作日志
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "通过省份获取地市数据失败",
                                         "/api/tmDdistrict/getCityByProvince",
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            return res

    # 通过地市获取区县数据
    def get_county_by_city(self, city_code, request):
        """
        根据地市代码获取对应的区县行政区划数据。

        :param city_code: 地市代码
        :param request: 请求对象，包含请求的相关信息
        :return: 包含区县行政区划数据的字典，若成功则包含行政区划信息，若失败则包含错误信息
        """
        res = ""
        # 记录操作开始时间
        start = time.perf_counter()
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 构建 SQL 查询语句，从 WJYY_GZ_TM_DISTRICT 表中查询父代码为指定地市代码的行政区划名称和代码
            sql = 'select dis_name,dis_code  from "WJYY_GZ_TM_DISTRICT" where parent_code={}'.format(city_code)
            # 获取数据库游标
            cursor = self.connection.cursor()
            # 执行 SQL 查询
            cursor.execute(sql)
            # 获取查询结果
            records = cursor.fetchall()
            data_list = []
            for record in records:
                obj = {}
                # 区县行政区划名称
                obj['county_name'] = str(record[0])
                # 区县行政区划代码
                obj['county_code'] = int(record[1])
                # 区县行政区划对应的 GeoJSON 文件路径
                obj['county_json'] = "district/{}.geoJson".format(obj['county_code'])
                data_list.append(obj)
            # 构建包含成功信息和行政区划数据的字典
            res = {
                'success': True,
                'total': len(data_list),
                "url_head": CommonHelper.get_url_head(),
                'info': data_list
            }

            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            logger.info("总共用时{}秒".format(t))
            # 插入操作日志，这里日志路径存在错误，应该是 "/api/tmDdistrict/getCountyByCity"
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "通过地市获取区县数据",
                                         "/api/tmDdistrict/getCityByProvince",
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录错误信息，包括异常描述、异常对象、异常发生的文件和行号
            logger.error("通过地市获取区县数据失败：" + str(exp))
            logger.error(exp)
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])  # 发生异常所在的文件
            logger.error(exp.__traceback__.tb_lineno)  # 发生异常所在的行数
            # 构建包含失败信息和错误描述的字典
            res = {
                'success': False,
                'info': "通过地市获取区县数据失败：{}".format(str(exp))
            }
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束时间
            end = time.perf_counter()
            # 计算操作总用时
            t = end - start
            logger.info("总共用时{}秒".format(t))
            # 插入异常操作日志，这里日志路径存在错误，应该是 "/api/tmDdistrict/getCountyByCity"
            LoggerHelper.insert_log_info(SysLog, request.auth.user, "通过地市获取区县数据失败",
                                         "/api/tmDdistrict/getCityByProvince",
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            return res

    # 单上传文件
    def upload_single_file(self, request):
        """
        上传单个文件到服务器，并将文件信息保存到数据库中。

        :param request: 请求对象，包含上传的文件信息
        :return: 包含上传结果的字典，若成功则包含成功信息和文件 UUID，若失败则包含错误信息
        """
        function_title = "上传文件"
        # 获取请求的 API 路径
        api_path = request.path
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        # 从请求中获取名为 'file' 的文件对象
        file = request.FILES.get('file')
        type = request.data['type']
        logger.info('上传文件是:%s' % file)
        # 检查上传文件存储目录是否存在，若不存在则创建
        if not os.path.exists(settings.UPLOAD_ROOT):
            logger.info("创建upload文件夹")
            os.makedirs(settings.UPLOAD_ROOT)
        res = ""
        try:
            # 若未选择文件
            if file is None:
                res = {
                    'success': False,
                    'info': "请选择要上传的文件"
                }
            else:
                data = {}
                # 生成唯一的文件 ID
                data['id'] = SnowflakeIDUtil.snowflakeId()
                # 提取文件名（不包含后缀）
                data['file_name'] = file.name[0:file.name.rfind('.')]
                # 提取文件后缀
                data['file_suffix'] = file.name[file.name.rfind('.') + 1:]
                # 记录创建用户 ID
                data['create_user_id'] = request.auth.user_id
                # 记录文件创建时间
                data['create_time'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                # 以二进制写入模式打开文件，将上传的文件内容逐行写入
                with open(os.path.join(settings.UPLOAD_ROOT, str(data['id']) + "." + str(data['file_suffix'])),
                          'wb') as f:
                    for i in file.readlines():
                        f.write(i)
                # 获取文件完整路径
                filepath = os.path.join(settings.UPLOAD_ROOT, str(data['id']) + "." + str(data['file_suffix']))
                # 获取文件大小
                file_size = os.path.getsize(filepath)
                data['file_size'] = file_size
                # 记录文件存储路径
                # data['path'] = "/" + str(data['id']) + "." + str(data['file_suffix'])
                data['path'] = filepath
                if type == 'task_slt':
                    del data["type"]
                    models.TtTaskSltFileData.objects.create(**data)
                elif type == 'dron_bask':
                    models.TtDronBaskFileData.objects.create(**data)
                elif type == 'alert_f':
                    data["alert_id"] = request.data['alert_id']
                    models.TtDronAlertFileData.objects.create(**data)
                else:
                    # 将文件信息保存到数据库
                    models.TtUploadFileData.objects.create(**data)
                res = {
                    'success': True,
                    'info': "上传文件成功",
                    'path':data['path'],
                    'file_uuid': data['id']
                }
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
        except Exception as exp:
            # 若出现异常，记录异常日志并返回异常处理结果
            res = LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                             request.auth.user, request,
                                                             function_title, None, exp)
        finally:
            return res

    # 上传自定义图标
    def upload_custom_image(self, request):
        """
        上传自定义图标到服务器，将图标信息保存到数据库，并关联到标注符号数据中。

        :param request: 请求对象，包含上传的图标信息
        :return: 包含上传结果的字典，若成功则包含成功信息和文件 UUID，若失败则包含错误信息
        """
        function_title = "上传自定义图标"
        # 获取请求的 API 路径
        api_path = request.path
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        # 从请求中获取名为 'file' 的文件对象
        file = request.FILES.get('file')
        logger.info('上传自定义图标是:%s' % file)
        # 构建自定义图标存储目录路径
        CUSTOM_IAMGE_ROOT = os.path.join(BASE_DIR, '{}'.format(APP_NAME), '{}'.format(STATIC_NAME), 'symbol', 'custom')
        # 检查自定义图标存储目录是否存在，若不存在则创建
        if not os.path.exists(CUSTOM_IAMGE_ROOT):
            logger.info("创建symbol/custom文件夹")
            os.makedirs(CUSTOM_IAMGE_ROOT)
        res = ""
        try:
            # 若未选择图标
            if file is None:
                res = {
                    'success': False,
                    'info': "请选择要上传的图标"
                }
            else:
                data = {}
                # 生成唯一的文件 ID
                data['id'] = SnowflakeIDUtil.snowflakeId()
                # 提取文件名（不包含后缀）
                data['file_name'] = file.name[0:file.name.rfind('.')]
                # 提取文件后缀
                data['file_suffix'] = file.name[file.name.rfind('.') + 1:]
                # 记录创建用户 ID
                data['create_user_id'] = request.auth.user_id
                # 记录文件创建时间
                data['create_time'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                # 以二进制写入模式打开文件，将上传的图标内容逐行写入
                with open(CUSTOM_IAMGE_ROOT + "/" + str(data['id']) + "." + str(data['file_suffix']),
                          'wb') as f:
                    for i in file.readlines():
                        f.write(i)
                # 获取图标完整路径
                filepath = CUSTOM_IAMGE_ROOT + "/" + str(data['id']) + "." + str(data['file_suffix'])
                # 获取图标大小
                file_size = os.path.getsize(filepath)
                data['file_size'] = file_size
                # 记录图标存储路径
                data['path'] = "symbol/custom/" + str(data['id']) + "." + str(data['file_suffix'])
                # 将图标信息保存到数据库
                models.TtUploadFileData.objects.create(**data)

                data2 = {}
                data2['id'] = data["id"]
                # TODO: 判断数据表里是否有同名的 cname，若有需要加后缀
                data2['cname'] = data['file_name']
                data2['ename'] = data['file_name']
                # 获取自定义标注类型的 ID
                data2['type'] = TtAnnotationType.objects.get(annotation_cname="自定义标注").id
                data2['remark'] = data['file_name']
                data2['sysmbol_path'] = data['path']
                data2['create_time'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                data2['create_user_id'] = request.auth.user_id
                # 将图标关联到标注符号数据中
                TtAnnotationSysmbolData.objects.create(**data2)
                res = {
                    'success': True,
                    'info': "上传自定义图标成功",
                    'file_uuid': data['id']
                }
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
        except Exception as exp:
            # 若出现异常，记录异常日志并返回异常处理结果
            res = LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                             request.auth.user, request,
                                                             function_title, None, exp)
        finally:
            return res

    # 多上传文件
    def upload_multi_files(self, request):
        """
        上传多个文件到服务器，并将每个文件的信息保存到数据库中。

        :param request: 请求对象，包含上传的多个文件信息
        :return: 包含上传结果的字典，若成功则包含成功信息和文件 UUID 列表，若失败则包含错误信息
        """
        function_title = "上传文件"
        # 获取请求的 API 路径
        api_path = request.path
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        # 从请求中获取名为 'files' 的文件列表
        files = request.FILES.getlist('files')
        type = request.data['type']
        logger.info('上传文件是:%s' % files)
        # 检查上传文件存储目录是否存在，若不存在则创建
        if not os.path.exists(settings.UPLOAD_ROOT):
            logger.info("创建upload文件夹")
            os.makedirs(settings.UPLOAD_ROOT)
        res = ""
        try:
            # 若未选择文件
            if files is None:
                res = {
                    'success': False,
                    'info': "请选择要上传的文件"
                }
            else:
                file_uuid_list = []
                for file in files:
                    data = {}
                    # 生成唯一的文件 ID
                    data['id'] = SnowflakeIDUtil.snowflakeId()
                    # 提取文件名（不包含后缀）
                    data['file_name'] = file.name[0:file.name.rfind('.')]
                    # 提取文件后缀
                    data['file_suffix'] = file.name[file.name.rfind('.') + 1:]
                    # 记录创建用户 ID
                    data['create_user_id'] = request.auth.user_id
                    # 记录文件创建时间
                    data['create_time'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    # 此处存在错误，应该是 data['id'] 而不是 data['file_id']
                    with open(settings.UPLOAD_ROOT + "/" + str(data['id']) + "." + str(data['file_suffix']),
                              'wb') as f:
                        for i in file.readlines():
                            f.write(i)
                    # 获取文件完整路径
                    filepath = settings.UPLOAD_ROOT + "/" + str(data['id']) + "." + str(data['file_suffix'])
                    # 获取文件大小
                    file_size = os.path.getsize(filepath)
                    data['file_size'] = file_size
                    # 记录文件存储路径
                    data['path'] = "\\" + str(data['id']) + "." + str(data['file_suffix'])
                    if type == 'dron_bask':
                        models.TtDronBaskFileData.objects.create(**data)
                    else:
                        # 将文件信息保存到数据库
                        models.TtUploadFileData.objects.create(**data)

                    file_uuid_list.append(data['id'])
                res = {
                    'success': True,
                    'info': "上传文件成功",
                    'file_uuid': file_uuid_list
                }
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, api_path, request.auth.user, request,
                                          function_title)
        except Exception as exp:
            # 若出现异常，记录异常日志并返回异常处理结果
            res = LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, api_path,
                                                             request.auth.user, request,
                                                             function_title, None, exp)
        finally:
            return res
