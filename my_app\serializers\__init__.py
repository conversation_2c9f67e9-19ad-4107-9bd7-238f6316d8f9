#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time : 2024-12-26 当前系统时间
# <AUTHOR> zhous
# @Email : <EMAIL>
# @File : __init__.py
# @Descr : 序列化器包初始化文件
# @Software: VSCode

# 导入通用序列化器
from .common import CommonResponseSerializer

# 导入前端序列化器
from .frontend.task_serializers import TaskCreateSerializer, TaskReadOnlySerializer
from .frontend.alert_serializers import AlertReadOnlySerializer
from .frontend.schedule_serializers import ScheduleReadOnlySerializer
from .frontend.drone_serializers import DroneReadOnlySerializer

# 导入后台管理序列化器
from .admin.task_serializers import TaskAdminSerializer, TaskReviewSerializer
from .admin.alert_serializers import AlertAdminSerializer
from .admin.schedule_serializers import ScheduleAdminSerializer
from .admin.drone_serializers import DroneAdminSerializer

# 原有的序化
from .serializers import *
__all__ = [
    # 通用序列化器
    'CommonResponseSerializer',
    
    # 前端序列化器
    'TaskCreateSerializer', 'TaskReadOnlySerializer',
    'AlertReadOnlySerializer',
    'ScheduleReadOnlySerializer', 
    'DroneReadOnlySerializer',
    
    # 后台管理序列化器
    'TaskAdminSerializer', 'TaskReviewSerializer',
    'AlertAdminSerializer',
    'ScheduleAdminSerializer',
    'DroneAdminSerializer',
]
