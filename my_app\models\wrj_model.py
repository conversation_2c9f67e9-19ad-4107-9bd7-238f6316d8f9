# -*- coding: utf-8 -*-
"""
业务模型 - 无人机相关数据模型
从 my_models.yw_model 迁移到 my_app.models.yw_model
"""
from django.db import models
from decimal import Decimal


class TtDroneTasks(models.Model):
    """无人机任务表"""

    class TaskStatus(models.TextChoices):
        PENDING_REVIEW = 'pending_review', '待审核'
        PENDING = 'pending', '待执行'
        RUNNING = 'running', '执行中'
        PENDING_ANALYSIS = 'pending_analysis', '待分析'
        ANALYZING = 'analyzing', '分析中'
        COMPLETED = 'completed', '已完成'
        CANCELLED = 'cancelled', '已取消'
        FAILED = 'failed', '执行失败'

    class TaskPurpose(models.TextChoices):
        PHOTOGRAPHY = 'photography', '拍摄'
        PANORAMIC = 'panoramic', '全景'
        THREE_DIMENSIONAL = '3d', '三维'
        ORTHOPHOTO = 'orthophoto', '正射'
        VIDEO = 'video', '视频'

    class TaskShape(models.TextChoices):
        POINT = 'Point', '点'
        LINE = 'LineString', '线'
        CIRCLE = 'Circle', '圆形'
        POLYGON = 'Polygon', '多边形'
        RECTANGLE = 'Rectangle', '正北矩形'

    class TaskUrgency(models.TextChoices):
        LOW = 'low', '低'
        MEDIUM = 'medium', '中'
        HIGH = 'high', '高'
        URGENT = 'urgent', '紧急'

    class ReviewStatus(models.TextChoices):
        PENDING = 'pending', '待审核'
        APPROVED = 'approved', '已通过'
        REJECTED = 'rejected', '已拒绝'
        REVISION_REQUIRED = 'revision_required', '需修改'

    # 主键和基础信息
    id = models.BigAutoField(primary_key=True, db_comment="任务ID")
    task_name = models.CharField(
        max_length=255,
        unique=True,
        db_index=True,
        db_comment='任务的唯一名称，用于标识和检索任务'
    )

    # 任务状态和属性
    task_status = models.CharField(
        max_length=20,
        choices=TaskStatus.choices,
        default=TaskStatus.PENDING_REVIEW,
        db_index=True,
        db_comment='任务状态'
    )
    task_purpose = models.CharField(
        max_length=20,
        choices=TaskPurpose.choices,
        db_comment='任务的主要用途类型'
    )
    task_urgency = models.CharField(
        max_length=10,
        choices=TaskUrgency.choices,
        default=TaskUrgency.MEDIUM,
        db_comment='任务的紧急程度级别'
    )

    # 地理信息
    task_shape = models.CharField(
        max_length=10,
        choices=TaskShape.choices,
        default=TaskShape.POLYGON,
        db_comment='任务区域的几何形状类型'
    )
    center_coordinates = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment='任务区域中心坐标，格式：经度,纬度'
    )
    perimeter = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        db_comment='任务区域的周长，单位：米'
    )
    area = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        default=Decimal('0.0000'),
        db_comment='任务区域的面积，单位：平方千米'
    )
    task_shape_points = models.TextField(
        default='{}',
        db_comment='区域几何数据，GeoJSON格式字符串'
    )
    location_detail = models.TextField(
        default='',
        db_comment='任务位置的详细描述信息'
    )

    # 无人机分配（外键关联）
    selected_drone = models.ForeignKey(
        'TtDroneBasicInfo',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_tasks'
    )

    # 任务详情和结果
    thumbnail = models.CharField(
        max_length=500,
        default='',
        db_comment='任务缩略图 image url'
    )
    mark_count = models.BigIntegerField(
        blank=True,
        default=0,
        null=True,
        db_comment='标记数量'
    )
    photo_count = models.BigIntegerField(
        blank=True,
        default=0,
        null=True,
        db_comment='照片数量'
    )
    remark = models.TextField(
        blank=True,
        null=True,
        db_comment='任务备注信息'
    )

    # 分析相关字段
    analysis_start_time = models.DateTimeField(
        null=True,
        blank=True,
        db_comment='分析开始时间'
    )
    analysis_end_time = models.DateTimeField(
        null=True,
        blank=True,
        db_comment='分析结束时间'
    )
    ai_analysis_result = models.TextField(
        default='{}',
        db_comment='AI分析结果摘要，JSON格式字符串'
    )

    # 审核相关字段
    review_status = models.CharField(
        max_length=20,
        choices=ReviewStatus.choices,
        default=ReviewStatus.PENDING,
        db_index=True,
        db_comment='审核状态'
    )
    reviewer_id = models.BigIntegerField(
        null=True,
        blank=True,
        db_comment='审核人ID'
    )
    review_time = models.DateTimeField(
        null=True,
        blank=True,
        db_comment='审核时间'
    )
    review_notes = models.TextField(
        blank=True,
        db_comment='审核备注'
    )

    # 审计字段
    created_by_id = models.BigIntegerField(
        null=True,
        blank=True,
        db_comment='创建用户ID'
    )
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    updated_by_id = models.BigIntegerField(
        null=True,
        blank=True,
        db_comment='更新用户ID'
    )
    update_time = models.DateTimeField(auto_now=True, db_comment='更新时间')

    class Meta:
        managed = True  # 启用Django管理
        db_table = 'tt_drone_tasks'
        db_table_comment = '任务表'
        indexes = [
            # 任务状态查询索引
            models.Index(fields=['task_status'], name='idx_task_status'),
            # 任务用途查询索引
            models.Index(fields=['task_purpose'], name='idx_task_purpose'),
            # 无人机任务查询索引
            models.Index(fields=['selected_drone', 'task_status'], name='idx_drone_task_status'),
            # 紧急程度查询索引
            models.Index(fields=['task_urgency', 'task_status'], name='idx_urgency_status'),
            # 创建时间索引
            models.Index(fields=['create_time'], name='idx_task_create_time'),
        ]

    def __str__(self):
        return f"{self.task_name} ({self.task_status})"

    def set_shape_geojson(self, geojson_data):
        """设置GeoJSON几何数据"""
        import json
        if isinstance(geojson_data, dict):
            self.task_shape_points = json.dumps(geojson_data, ensure_ascii=False)
        else:
            self.task_shape_points = str(geojson_data)

    def get_shape_geojson(self):
        """获取GeoJSON几何数据"""
        import json
        try:
            if self.task_shape_points:
                return json.loads(self.task_shape_points)
            return {}
        except (json.JSONDecodeError, TypeError):
            return {}

    def set_coordinates(self, coordinates, shape_type=None):
        """根据坐标和形状类型设置GeoJSON数据"""
        if not shape_type:
            shape_type = self.task_shape

        geojson_data = {}
        if shape_type == 'Point':
            geojson_data = {
                "type": "Point",
                "coordinates": coordinates  # [lng, lat]
            }
        elif shape_type == 'LineString':
            geojson_data = {
                "type": "LineString",
                "coordinates": coordinates  # [[lng,lat], [lng,lat]...]
            }
        elif shape_type == 'Polygon':
            geojson_data = {
                "type": "Polygon",
                "coordinates": [coordinates]  # [[[lng,lat], [lng,lat]...]]
            }
        elif shape_type == 'Circle':
            # 圆形使用中心点和半径表示
            geojson_data = {
                "type": "Circle",
                "coordinates": coordinates[0],  # 中心点 [lng, lat]
                "radius": coordinates[1] if len(coordinates) > 1 else 100  # 半径（米）
            }
        elif shape_type == 'Rectangle':
            # 矩形使用对角线两点表示
            geojson_data = {
                "type": "Rectangle",
                "coordinates": coordinates  # [[lng1,lat1], [lng2,lat2]]
            }

        self.set_shape_geojson(geojson_data)


class TtDroneBasicInfo(models.Model):
    """无人机设备基础信息表"""

    class DroneStatus(models.TextChoices):
        ONLINE = 'online', '在线'
        WORKING = 'working', '工作中'
        OFFLINE = 'offline', '离线'
        ABNORMAL = 'abnormal', '异常'
        MAINTENANCE = 'maintenance', '维护'

    class DeploymentStatus(models.TextChoices):
        DEPLOYED = 'deployed', '已部署'
        PENDING = 'pending', '待部署'
        MAINTENANCE = 'maintenance', '维护中'
        WITHDRAWN = 'withdrawn', '已撤回'

    class DroneBrand(models.TextChoices):
        DJI = 'dji', '大疆'
        AUTEL = 'autel', '道通'
        PARROT = 'parrot', 'Parrot'
        YUNEEC = 'yuneec', '昊翔'
        OTHER = 'other', '其他'

    # 主键和基础信息
    drone_id = models.BigAutoField(primary_key=True, db_comment="无人机ID")
    drone_serial = models.CharField(
        unique=True,
        max_length=50,
        db_index=True,
        db_comment='无人机序列号，设备出厂唯一编号'
    )

    # 设备规格（必填字段）
    brand = models.CharField(
        max_length=20,
        choices=DroneBrand.choices,
        db_comment='无人机品牌'
    )
    model = models.CharField(
        max_length=50,
        db_comment='无人机型号（如 Mavic 3、Phantom 4）'
    )

    # 技术参数
    weight_kg = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment='无人机重量（千克）'
    )
    dimensions = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment='无人机尺寸（展开/折叠状态）'
    )
    max_flight_time_min = models.IntegerField(
        blank=True,
        null=True,
        db_comment='最大续航时间（分钟）'
    )
    camera_model = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment='搭载的相机型号'
    )
    camera_resolution = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment='相机分辨率（如 4K/60fps）'
    )
    gps_precision = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment='GPS定位精度（如厘米级/米级）'
    )
    obstacle_avoidance = models.BooleanField(
        default=False,
        db_comment='是否支持避障功能'
    )
    battery_capacity_mah = models.IntegerField(
        blank=True,
        null=True,
        db_comment='电池容量（毫安时）'
    )

    # 状态和管理信息
    status = models.CharField(
        max_length=20,
        choices=DroneStatus.choices,
        default=DroneStatus.OFFLINE,
        db_index=True,
        db_comment='设备状态'
    )
    firmware_version = models.CharField(
        max_length=30,
        blank=True,
        null=True,
        db_comment='当前固件版本号'
    )

    # 组织管理
    department = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_index=True,
        db_comment='所属部门/团队'
    )
    responsible_person = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment='设备负责人'
    )

    # 采购信息
    purchase_date = models.DateField(
        blank=True,
        null=True,
        db_comment='采购日期'
    )
    purchase_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment='采购价格（元）'
    )

    # 部署信息
    deployment_location = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment='部署位置名称（如"北京市朝阳区森林公园"）'
    )
    deployment_coordinates = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment='部署位置坐标，格式：经度,纬度'
    )
    deployment_address = models.TextField(
        blank=True,
        null=True,
        db_comment='详细部署地址'
    )
    deployment_date = models.DateField(
        blank=True,
        null=True,
        db_comment='部署日期'
    )
    deployment_status = models.CharField(
        max_length=20,
        choices=DeploymentStatus.choices,
        default=DeploymentStatus.PENDING,
        db_index=True,
        db_comment='部署状态'
    )

    # 实时连接信息
    websocket_endpoint = models.URLField(
        blank=True,
        db_comment='WebSocket连接地址'
    )
    mqtt_endpoint = models.URLField(
        blank=True,
        db_comment='MQTT连接地址'
    )
    api_token = models.CharField(
        max_length=500,
        blank=True,
        db_comment='API访问令牌'
    )

    # 视频流地址
    gimbal_stream_url = models.URLField(
        blank=True,
        db_comment='云台摄像头视频流'
    )
    nest_indoor_stream_url = models.URLField(
        blank=True,
        db_comment='巢内监控流'
    )
    nest_outdoor_stream_url = models.URLField(
        blank=True,
        db_comment='巢外监控流'
    )

    # 连接状态
    connection_status = models.CharField(
        max_length=20,
        default='disconnected',
        db_comment='连接状态'
    )
    last_heartbeat = models.DateTimeField(
        null=True,
        blank=True,
        db_comment='最后心跳时间'
    )

    # 审计字段
    created_by_id = models.BigIntegerField(
        null=True,
        blank=True,
        db_comment='创建用户ID'
    )
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    updated_by_id = models.BigIntegerField(
        null=True,
        blank=True,
        db_comment='更新用户ID'
    )
    update_time = models.DateTimeField(auto_now=True, db_comment='更新时间')

    class Meta:
        managed = True  # 启用Django管理
        db_table = 'tt_drone_basic_info'
        db_table_comment = '无人机设备基础信息表'
        indexes = [
            # 状态查询索引
            models.Index(fields=['status'], name='idx_drone_status'),
            # 部门查询索引
            models.Index(fields=['department', 'status'], name='idx_drone_dept_status'),
            # 品牌型号查询索引
            models.Index(fields=['brand', 'model'], name='idx_drone_brand_model'),
            # 部署状态查询索引
            models.Index(fields=['deployment_status'], name='idx_drone_deployment_status'),
            # 部署位置查询索引
            models.Index(fields=['deployment_location'], name='idx_drone_deployment_location'),
            # 创建时间索引
            models.Index(fields=['create_time'], name='idx_drone_create_time'),
        ]

    def __str__(self):
        return f"{self.drone_serial} ({self.brand} {self.model})"


class TtDroneAlerts(models.Model):
    """告警记录表"""

    class AlertParentType(models.IntegerChoices):
        """预警父类类型"""
        TRAFFIC_SAFETY = 1, '交通安全'
        AGRICULTURAL_WATER = 2, '农林水利'
        SAFETY_PRODUCTION = 3, '安全生产'
        ADVERTISING = 4, '宣传广告'
        FIRE_SAFETY = 5, '消防安全'
        CITY_ENVIRONMENT = 6, '市容环境'
        SECURITY_RISK = 7, '治安隐患'
        ECOLOGICAL_ENVIRONMENT = 8, '生态环境'
        NATURAL_DISASTER = 9, '自然灾害'
        OTHER = 99, '其他'

    class AlertType(models.IntegerChoices):
        """具体预警类型"""
        # 交通安全类 (10-19)
        ROAD_DAMAGE = 10, '道路损坏'
        VEHICLE_ILLEGAL_PARKING = 11, '车辆违停'
        TRAFFIC_CONGESTION = 12, '交通拥堵'
        ROAD_OCCUPATION = 13, '占道经营'
        FACILITY_DAMAGE = 14, '设施损坏'
        STREET_LIGHT_FAILURE = 15, '路灯故障'
        MANHOLE_COVER_MISSING = 16, '井盖缺失'

        # 农林水利类 (20-29)
        PINE_WILT_DISEASE = 20, '松材线虫病'
        PEST_DISEASE = 21, '病虫害'
        FOREST_FIRE = 22, '森林火灾'
        WATER_POLLUTION = 23, '水域污染'
        RIVER_FLOATING_OBJECTS = 24, '河道漂浮物'

        # 安全生产类 (30-39)
        SAFETY_HAZARD = 30, '安全隐患'
        DANGEROUS_GOODS = 31, '危险品'
        CONSTRUCTION_SITE = 32, '建筑工地'
        UNCOVERED_CONSTRUCTION = 33, '工地未苫盖'
        UNDER_CONSTRUCTION_FARMHOUSE = 34, '在建农房'

        # 宣传广告类 (40-49)
        # 暂无具体类型

        # 消防安全类 (50-59)
        FIRE_ESCAPE_BLOCKED = 50, '消防通道堵塞'

        # 市容环境类 (60-79)
        EXPOSED_GARBAGE = 60, '暴露垃圾'
        ILLEGAL_CONSTRUCTION = 61, '违章建筑'
        MOBILE_VENDORS = 62, '流动摊贩'
        ACCUMULATED_CONSTRUCTION_WASTE = 63, '积存建筑垃圾'
        GARBAGE_DUMPING = 64, '垃圾堆放'

        # 治安隐患类 (80-89)
        ABNORMAL_GATHERING = 80, '异常聚集'

        # 生态环境类 (90-99)
        NOISE_POLLUTION = 90, '噪音污染'
        LIGHT_POLLUTION = 91, '光污染'
        AIR_POLLUTION = 92, '大气污染'
        SOIL_POLLUTION = 93, '土壤污染'

        # 自然灾害类 (100-109)
        # 暂无具体类型

        # 其他类 (110-119)
        OTHER_ENVIRONMENTAL = 110, '其他环境问题'
        OTHER_SAFETY = 111, '其他安全问题'
        OTHER = 119, '其他'

    class AlertLevel(models.IntegerChoices):
        INFO = 1, '信息'
        LOW = 2, '低级'
        MEDIUM = 3, '中级'
        HIGH = 4, '高级'
        CRITICAL = 5, '严重'

    class AlertStatus(models.IntegerChoices):
        NEW = 0, '新发现'
        PROCESSING = 1, '反馈处理'
        COMPLETED = 2, '处理完成'
        IGNORED = 3, '已忽略'
        FALSE_ALARM = 4, '误报'

    # 主键和基础信息
    alert_id = models.AutoField(primary_key=True, db_comment='告警记录唯一标识')

    # 告警时间（重要：支持时间范围查询）
    alert_time = models.DateTimeField(
        db_index=True,
        db_comment='告警发生时间（带时区）'
    )

    # 告警分类和级别
    alert_parent_type = models.IntegerField(
        choices=AlertParentType.choices,
        default=AlertParentType.OTHER,
        db_index=True,
        db_comment='预警父类类型'
    )
    alert_type_id = models.IntegerField(
        choices=AlertType.choices,
        db_index=True,
        db_comment='具体预警类型'
    )
    alert_level = models.SmallIntegerField(
        choices=AlertLevel.choices,
        db_index=True,
        db_comment='告警级别（1-5级，1为最低，5为最高）'
    )

    # 关联信息（外键）
    task = models.ForeignKey(
        'TtDroneTasks',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='alerts'
    )
    drone = models.ForeignKey(
        'TtDroneBasicInfo',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='drone_alerts'
    )

    # 位置信息
    alert_location = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_comment='告警发生位置'
    )

    # 地理信息（GeoJSON格式）
    alert_geojson = models.TextField(
        blank=True,
        null=True,
        db_comment='预警地理信息（GeoJSON格式）'
    )

    # 告警详情
    notes = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_comment='告警发生描述'
    )

    # 地理信息（点和面数据）
    points = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_comment='警告点'
    )
    areas = models.CharField(
        max_length=2000,
        blank=True,
        null=True,
        db_comment='警告面'
    )

    # 处理状态
    status = models.IntegerField(
        choices=AlertStatus.choices,
        default=AlertStatus.NEW,
        db_index=True,
        db_comment='处理状态'
    )

    # 保留原有字段（兼容性）
    flight_id = models.BigIntegerField(
        blank=True,
        null=True,
        db_comment='飞行记录id'
    )
    drone_type = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment='无人机类型'
    )

    # 审计字段
    created_by_id = models.BigIntegerField(
        null=True,
        blank=True,
        db_comment='创建用户ID'
    )
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    updated_by_id = models.BigIntegerField(
        null=True,
        blank=True,
        db_comment='更新用户ID'
    )
    update_time = models.DateTimeField(auto_now=True, db_comment='更新时间')

    class Meta:
        managed = True  # 启用Django管理
        db_table = 'tt_drone_alerts'
        db_table_comment = '告警记录'
        indexes = [
            # 告警时间范围查询索引（重要）
            models.Index(fields=['alert_time'], name='idx_alert_time'),
            models.Index(fields=['alert_time', 'status'], name='idx_alert_time_status'),
            # 告警类型和级别查询索引
            models.Index(fields=['alert_type_id'], name='idx_alert_type'),
            models.Index(fields=['alert_level'], name='idx_alert_level'),
            models.Index(fields=['alert_type_id', 'alert_level'], name='idx_alert_type_level'),
            # 状态查询索引
            models.Index(fields=['status'], name='idx_alert_status'),
            # 任务关联查询索引
            models.Index(fields=['task', 'alert_type_id'], name='idx_task_alert_type'),
            # 创建时间索引
            models.Index(fields=['create_time'], name='idx_alert_create_time'),
        ]

    def __str__(self):
        return f"告警{self.alert_id} (级别{self.alert_level}) - {self.alert_time}"


class TtFlightSchedule(models.Model):
    """航线排期表"""

    class ScheduleStatus(models.TextChoices):
        PLANNED = 'planned', '已计划'
        EXECUTING = 'executing', '执行中'
        COMPLETED = 'completed', '已完成'
        CANCELLED = 'cancelled', '已取消'

    # 主键
    schedule_id = models.BigAutoField(
        primary_key=True,
        db_comment='排期ID'
    )

    # 基本信息
    schedule_name = models.CharField(
        max_length=255,
        db_comment='排期名称'
    )

    # 关联关系
    task = models.ForeignKey(
        'TtDroneTasks',
        on_delete=models.CASCADE,
        related_name='flight_schedules',
        null=True,
        blank=True
    )
    drone = models.ForeignKey(
        'TtDroneBasicInfo',
        on_delete=models.CASCADE,
        related_name='drone_flight_schedules'
    )

    # 计划信息
    planned_start_time = models.DateTimeField(
        db_comment='计划开始时间'
    )
    planned_end_time = models.DateTimeField(
        db_comment='计划结束时间'
    )
    estimated_duration = models.IntegerField(
        db_comment='预计飞行时间（分钟）'
    )

    # 路线信息
    route_geojson = models.TextField(
        db_comment='飞行路线GeoJSON格式'
    )
    waypoints_count = models.IntegerField(
        db_comment='航点数量'
    )
    total_distance = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        db_comment='总距离（米）'
    )

    # 状态信息
    schedule_status = models.CharField(
        max_length=20,
        choices=ScheduleStatus.choices,
        default=ScheduleStatus.PLANNED,
        db_index=True,
        db_comment='排期状态'
    )

    # 执行信息
    actual_start_time = models.DateTimeField(
        null=True,
        blank=True,
        db_comment='实际开始时间'
    )
    actual_end_time = models.DateTimeField(
        null=True,
        blank=True,
        db_comment='实际结束时间'
    )

    # 审计字段
    created_by_id = models.BigIntegerField(
        null=True,
        blank=True,
        db_comment='创建用户ID'
    )
    create_time = models.DateTimeField(
        auto_now_add=True,
        db_comment='创建时间'
    )
    updated_by_id = models.BigIntegerField(
        null=True,
        blank=True,
        db_comment='更新用户ID'
    )
    update_time = models.DateTimeField(
        auto_now=True,
        db_comment='更新时间'
    )

    class Meta:
        managed = True
        db_table = 'tt_flight_schedule'
        db_table_comment = '航线排期表'
        indexes = [
            # 排期状态查询索引
            models.Index(fields=['schedule_status'], name='idx_schedule_status'),
            # 无人机排期查询索引
            models.Index(fields=['drone', 'schedule_status'], name='idx_drone_schedule_status'),
            # 任务排期查询索引
            models.Index(fields=['task'], name='idx_task_schedule'),
            # 计划时间查询索引
            models.Index(fields=['planned_start_time'], name='idx_planned_start_time'),
            # 创建时间索引
            models.Index(fields=['create_time'], name='idx_schedule_create_time'),
        ]

    def __str__(self):
        return f"{self.schedule_name} - {self.schedule_status}"

