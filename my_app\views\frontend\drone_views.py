#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time : 2024-12-26 当前系统时间
# <AUTHOR> zhous
# @Email : <EMAIL>
# @File : drone_views.py
# @Descr : 前端无人机视图
# @Software: VSCode

from rest_framework import viewsets
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from my_app.models import TtDroneBasicInfo
from my_app.serializers.frontend.drone_serializers import DroneReadOnlySerializer
from my_app.serializers.common import CommonResponseSerializer
from my_app.views.base_views import SecureModelViewSet

class DroneFrontendViewSet(viewsets.ReadOnlyModelViewSet, SecureModelViewSet):
    """
    前端无人机管理API
    提供无人机查询功能
    """
    queryset = TtDroneBasicInfo.objects.all().order_by('-create_time')
    serializer_class = DroneReadOnlySerializer
    http_method_names = ['get']  # 只允许查询

    # 配置过滤和排序
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['status', 'brand', 'deployment_status']
    ordering_fields = ['create_time', 'drone_serial', 'status']
    ordering = ['-create_time']
    
    @swagger_auto_schema(
        operation_description="获取无人机列表",
        operation_summary="无人机列表查询",
        manual_parameters=[
            openapi.Parameter('status', openapi.IN_QUERY, description="设备状态", type=openapi.TYPE_STRING),
            openapi.Parameter('brand', openapi.IN_QUERY, description="设备品牌", type=openapi.TYPE_STRING),
            openapi.Parameter('deployment_status', openapi.IN_QUERY, description="部署状态", type=openapi.TYPE_STRING),
            openapi.Parameter('ordering', openapi.IN_QUERY, description="排序字段", type=openapi.TYPE_STRING),
        ],
        responses={
            200: CommonResponseSerializer,
            401: CommonResponseSerializer
        },
        tags=['前端-无人机查询']
    )
    def list(self, request, *args, **kwargs):
        """获取无人机列表"""
        response = super().list(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="无人机列表获取成功"
        ))
    
    @swagger_auto_schema(
        operation_description="获取无人机详情",
        operation_summary="无人机详情查询",
        responses={
            200: CommonResponseSerializer,
            404: CommonResponseSerializer
        },
        tags=['前端-无人机查询']
    )
    def retrieve(self, request, *args, **kwargs):
        """获取无人机详情"""
        response = super().retrieve(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="无人机详情获取成功"
        ))
