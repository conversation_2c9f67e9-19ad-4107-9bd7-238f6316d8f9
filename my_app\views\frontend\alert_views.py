#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time : 2024-12-26 当前系统时间
# <AUTHOR> zhous
# @Email : <EMAIL>
# @File : alert_views.py
# @Descr : 前端预警视图
# @Software: VSCode

from rest_framework import viewsets
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from my_app.models import TtDroneAlerts
from my_app.serializers.frontend.alert_serializers import AlertReadOnlySerializer
from my_app.serializers.common import CommonResponseSerializer
from my_app.views.base_views import SecureModelViewSet

class AlertFrontendViewSet(viewsets.ReadOnlyModelViewSet, SecureModelViewSet):
    """
    前端预警管理API
    提供预警查询功能
    """
    queryset = TtDroneAlerts.objects.all().order_by('-alert_time')
    serializer_class = AlertReadOnlySerializer
    http_method_names = ['get']  # 只允许查询

    # 配置过滤和排序
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['alert_level', 'status', 'alert_type_id', 'task', 'drone']
    ordering_fields = ['alert_time', 'alert_level', 'status']
    ordering = ['-alert_time']
    
    @swagger_auto_schema(
        operation_description="获取预警列表",
        operation_summary="预警列表查询",
        manual_parameters=[
            openapi.Parameter('alert_level', openapi.IN_QUERY, description="预警级别", type=openapi.TYPE_INTEGER),
            openapi.Parameter('status', openapi.IN_QUERY, description="预警状态", type=openapi.TYPE_INTEGER),
            openapi.Parameter('alert_type_id', openapi.IN_QUERY, description="预警类型", type=openapi.TYPE_INTEGER),
            openapi.Parameter('task', openapi.IN_QUERY, description="关联任务ID", type=openapi.TYPE_INTEGER),
            openapi.Parameter('drone', openapi.IN_QUERY, description="关联无人机ID", type=openapi.TYPE_INTEGER),
            openapi.Parameter('ordering', openapi.IN_QUERY, description="排序字段", type=openapi.TYPE_STRING),
        ],
        responses={
            200: CommonResponseSerializer,
            401: CommonResponseSerializer
        },
        tags=['前端-预警查询']
    )
    def list(self, request, *args, **kwargs):
        """获取预警列表"""
        response = super().list(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="预警列表获取成功"
        ))
    
    @swagger_auto_schema(
        operation_description="获取预警详情",
        operation_summary="预警详情查询",
        responses={
            200: CommonResponseSerializer,
            404: CommonResponseSerializer
        },
        tags=['前端-预警查询']
    )
    def retrieve(self, request, *args, **kwargs):
        """获取预警详情"""
        response = super().retrieve(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="预警详情获取成功"
        ))
