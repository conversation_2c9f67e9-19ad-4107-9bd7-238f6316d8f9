#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time : 2024-12-26 当前系统时间
# <AUTHOR> zhous
# @Email : <EMAIL>
# @File : schedule_views.py
# @Descr : 前端航线视图
# @Software: VSCode

from rest_framework import viewsets
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from my_app.models import TtFlightSchedule
from my_app.serializers.frontend.schedule_serializers import ScheduleReadOnlySerializer
from my_app.serializers.common import CommonResponseSerializer
from my_app.views.base_views import SecureModelViewSet

class ScheduleFrontendViewSet(viewsets.ReadOnlyModelViewSet, SecureModelViewSet):
    """
    前端航线管理API
    提供航线查询功能
    """
    queryset = TtFlightSchedule.objects.all().order_by('-create_time')
    serializer_class = ScheduleReadOnlySerializer
    http_method_names = ['get']  # 只允许查询

    # 配置过滤和排序
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['schedule_status', 'drone', 'task']
    ordering_fields = ['create_time', 'schedule_status']
    ordering = ['-create_time']
    
    @swagger_auto_schema(
        operation_description="获取航线列表",
        operation_summary="航线列表查询",
        manual_parameters=[
            openapi.Parameter('schedule_status', openapi.IN_QUERY, description="航线状态", type=openapi.TYPE_STRING),
            openapi.Parameter('drone', openapi.IN_QUERY, description="关联无人机ID", type=openapi.TYPE_INTEGER),
            openapi.Parameter('task', openapi.IN_QUERY, description="关联任务ID", type=openapi.TYPE_INTEGER),
            openapi.Parameter('ordering', openapi.IN_QUERY, description="排序字段", type=openapi.TYPE_STRING),
        ],
        responses={
            200: CommonResponseSerializer,
            401: CommonResponseSerializer
        },
        tags=['前端-航线查询']
    )
    def list(self, request, *args, **kwargs):
        """获取航线列表"""
        response = super().list(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="航线列表获取成功"
        ))
    
    @swagger_auto_schema(
        operation_description="获取航线详情",
        operation_summary="航线详情查询",
        responses={
            200: CommonResponseSerializer,
            404: CommonResponseSerializer
        },
        tags=['前端-航线查询']
    )
    def retrieve(self, request, *args, **kwargs):
        """获取航线详情"""
        response = super().retrieve(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="航线详情获取成功"
        ))
