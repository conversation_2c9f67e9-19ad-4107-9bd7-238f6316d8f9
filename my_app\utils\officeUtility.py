"""
#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
@Project :cysxydai_service
@File    :officeUtility.py
@IDE     :PyCharm
<AUTHOR>
@Date    :2024/8/29 17:31
@Descr:
"""
import os.path
import uuid

import pandas as pd
from docx import Document
from vgis_office.vgis_excel.excelTools import ExcelHelper

from my_app.utils.numberUtility import NumberHelper
from my_project.settings import UPLOAD_ROOT


class OfficeHelper:
    def __init__(self):
        # 类的初始化方法，当前为空，可根据需求添加初始化逻辑
        pass

    @staticmethod
    def split_excel_to_word(excel_path, word_template_path):
        """
        根据 Excel 文件内容，结合 Word 模板文件，生成多个 Word 文档。
        若 Excel 中只有一个管控单元，生成一个 Word 文档；若有多个管控单元，生成 N + 1 个 Word 文档，最后一个是汇总文档。

        :param excel_path: Excel 文件的路径
        :param word_template_path: Word 模板文件的路径
        :return: 生成的 Word 文档的路径列表
        """
        word_output_paths = []
        # 获取 Excel 文件的后缀名
        file_suffix = os.path.splitext(excel_path)[1]
        if file_suffix.lower() == ".xlsx":
            # 若为 .xlsx 文件，使用 openpyxl 引擎读取
            excel_data_obj = pd.read_excel(excel_path, engine='openpyxl', keep_default_na=False)
        else:
            # 其他类型文件，使用默认引擎读取
            excel_data_obj = pd.read_excel(excel_path, keep_default_na=False)

        # 获取 Excel 中各字段的列索引
        control_unit_name_field_index = ExcelHelper.get_field_index_by_name_in_excel(excel_data_obj, "涉及管控单元名称")
        space_layout_constraints_field_index = ExcelHelper.get_field_index_by_name_in_excel(excel_data_obj,
                                                                                            "空间布局约束")
        pollutant_emission_control_field_index = ExcelHelper.get_field_index_by_name_in_excel(excel_data_obj,
                                                                                              "污染物排放管控")
        environmental_risk_prevention_field_index = ExcelHelper.get_field_index_by_name_in_excel(excel_data_obj,
                                                                                                 "环境风险防范")
        resource_utilization_efficiency_field_index = ExcelHelper.get_field_index_by_name_in_excel(excel_data_obj,
                                                                                                   "资源利用效率")

        # 只有一个管控单元，就只生成一个管控单元的 Word 文档
        if len(excel_data_obj) == 1:
            word_output_path = OfficeHelper.build_word_by_row(excel_data_obj, 0, word_template_path,
                                                              control_unit_name_field_index,
                                                              space_layout_constraints_field_index,
                                                              pollutant_emission_control_field_index,
                                                              environmental_risk_prevention_field_index,
                                                              resource_utilization_efficiency_field_index)
            word_output_paths.append(word_output_path)
        # 如果有 N 个管控单元，则生成 N + 1 个管控单元的 Word 文档，最后一个 Word 文档是汇总所有单元的文档
        # 如果管控单元只有两个，而且有一个还为空，则最后就只生成一个管控单元的 Word 文档，不用汇总
        else:
            # 生成行索引列表，最后一个索引 -1 表示汇总所有单元
            row_index_array = list(range(0, len(excel_data_obj)))
            row_index_array.append(-1)
            for row_index in row_index_array:
                word_output_path = OfficeHelper.build_word_by_row(excel_data_obj, row_index, word_template_path,
                                                                  control_unit_name_field_index,
                                                                  space_layout_constraints_field_index,
                                                                  pollutant_emission_control_field_index,
                                                                  environmental_risk_prevention_field_index,
                                                                  resource_utilization_efficiency_field_index)
                word_output_paths.append(word_output_path)

        return word_output_paths

    @staticmethod
    def build_word_by_row(excel_data_obj, row_index, word_template_path, control_unit_name_field_index,
                          space_layout_constraints_field_index, pollutant_emission_control_field_index,
                          environmental_risk_prevention_field_index, resource_utilization_efficiency_field_index):
        """
        根据 Excel 中的行数据和 Word 模板文件，生成单个 Word 文档。

        :param excel_data_obj: 包含 Excel 数据的 Pandas DataFrame 对象
        :param row_index: 要处理的 Excel 行索引，-1 表示汇总所有行
        :param word_template_path: Word 模板文件的路径
        :param control_unit_name_field_index: 涉及管控单元名称字段的列索引
        :param space_layout_constraints_field_index: 空间布局约束字段的列索引
        :param pollutant_emission_control_field_index: 污染物排放管控字段的列索引
        :param environmental_risk_prevention_field_index: 环境风险防范字段的列索引
        :param resource_utilization_efficiency_field_index: 资源利用效率字段的列索引
        :return: 生成的 Word 文档的路径，如果四个要求字段都为空则返回 None
        """
        # 每个管控单元
        if row_index != -1:
            # 获取指定行的数据
            row_values = excel_data_obj.values[row_index]
            control_unit_name_field_value = row_values[control_unit_name_field_index]
            space_layout_constraints_field_value = row_values[space_layout_constraints_field_index]
            pollutant_emission_control_field_value = row_values[pollutant_emission_control_field_index]
            environmental_risk_prevention_field_value = row_values[environmental_risk_prevention_field_index]
            resource_utilization_efficiency_field_value = row_values[resource_utilization_efficiency_field_index]
        # 对所有管控单元汇总
        else:
            space_layout_constraints_field_value = ""
            pollutant_emission_control_field_value = ""
            environmental_risk_prevention_field_value = ""
            resource_utilization_efficiency_field_value = ""
            for row_index2 in range(len(excel_data_obj.values)):
                row_values2 = excel_data_obj.values[row_index2]
                control_unit_name_field_value = row_values2[control_unit_name_field_index]
                # 排除管控单元名称为 / 的情况
                if control_unit_name_field_value.strip() != "/":
                    # 拼接空间布局约束信息
                    space_layout_constraints_field_value += "\n\n" + control_unit_name_field_value + "的空间布局约束如下：\n\n"
                    space_layout_constraints_field_value += row_values2[
                        space_layout_constraints_field_index] if row_values2[
                                                                     space_layout_constraints_field_index].strip() != "" else "无" + "\n"
                    # 拼接污染物排放管控信息
                    pollutant_emission_control_field_value += "\n\n" + control_unit_name_field_value + "的污染物排放管控如下：\n\n"
                    pollutant_emission_control_field_value += row_values2[
                        pollutant_emission_control_field_index] if row_values2[
                                                                       pollutant_emission_control_field_index].strip() != "" else "无" + "\n"
                    # 拼接环境风险防范信息
                    environmental_risk_prevention_field_value += "\n\n" + control_unit_name_field_value + "的环境风险防范如下：\n\n"
                    environmental_risk_prevention_field_value += row_values2[
                        environmental_risk_prevention_field_index] if \
                        row_values2[environmental_risk_prevention_field_index].strip() != "" else "无" + "\n"
                    # 拼接资源利用效率信息
                    resource_utilization_efficiency_field_value += "\n\n" + control_unit_name_field_value + "的资源利用效率如下：\n\n"
                    resource_utilization_efficiency_field_value += row_values2[
                        resource_utilization_efficiency_field_index] if \
                        row_values2[resource_utilization_efficiency_field_index].strip() != "" else "无" + "\n"
            control_unit_name_field_value = "全部管控单元"

        # 读取报告模板
        data_doc = Document(word_template_path)
        # 对报告里的文本信息进行动态更新
        obj = dict()
        # 将 excel 里管控单元文本的一、二、三、四等降级为（一）、（二）、（三）、（四）等，以匹配最上面的编号
        obj["{space_layout_constraints}"] = NumberHelper.degrade_big_number(space_layout_constraints_field_value)
        obj["{pollutant_emission_control}"] = NumberHelper.degrade_big_number(pollutant_emission_control_field_value)
        obj["{environmental_risk_prevention}"] = NumberHelper.degrade_big_number(
            environmental_risk_prevention_field_value)
        obj["{resource_utilization_efficiency}"] = NumberHelper.degrade_big_number(
            resource_utilization_efficiency_field_value)

        # 循环所有段落，解决关键词书签被 run 拆分的问题，如 {space_layout_constraints} 被拆分成了 {、space_layout_constraints、} 三部分，导致匹配不上
        _map_dict = obj
        for _p in data_doc.paragraphs:
            runs = _p.runs
            # 定义一个空的匹配词
            _temp = ''
            for run in runs:
                # 若替换词的开头在 run.text 中，结尾不在，且匹配词为空，则取出替换词的开头放入匹配词
                if '{' in run.text and '}' not in run.text and _temp == '':
                    _ext = '{' + run.text.split('{')[1]
                    _temp += _ext
                    run.text = run.text.replace(_ext, '')
                    continue

                if _temp:
                    # 如果匹配词不为空 且 替换词的结尾在 run.text 中，则取出替换词的结尾放入匹配词
                    if '}' in run.text:
                        _ext = run.text.split('}')[0] + '}'
                        _temp += _ext
                        # 说明已经将替换词完整取出，根据词映射关系进行替换
                        run.text = run.text.replace(_ext, str(_map_dict[_temp]))
                        _temp = ''
                    else:
                        # 否则 将 run.text 追加放入匹配词
                        _temp += run.text
                        run.text = ''
                    continue

                for _key, _val in _map_dict.items():
                    if _key in run.text:
                        run.text = run.text.replace(_key, str(_val))

        # 检查四个要求字段是否至少有一个不为空
        if space_layout_constraints_field_value.strip() != "" or pollutant_emission_control_field_value.strip() != "" or environmental_risk_prevention_field_value.strip() != "" and resource_utilization_efficiency_field_value.strip() != "":
            # 创建 upload 文件夹
            file_name = "{}应执行准入清单.docx".format(control_unit_name_field_value)
            temp_upload_dir = os.path.join(UPLOAD_ROOT, str(uuid.uuid4()))
            if not os.path.exists(temp_upload_dir):
                os.makedirs(temp_upload_dir)
            word_output_path = os.path.join(temp_upload_dir, file_name)
            # 保存生成报告
            data_doc.save(word_output_path)
            return word_output_path
        # 如果四个要求都是空的，则返回空对象路径
        else:
            return None