# Generated by Django 4.2.2 on 2025-08-25 19:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my_app', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='deployment_address',
            field=models.TextField(blank=True, db_comment='详细部署地址', null=True),
        ),
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='deployment_coordinates',
            field=models.CharField(blank=True, db_comment='部署位置坐标，格式：经度,纬度', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='deployment_date',
            field=models.DateField(blank=True, db_comment='部署日期', null=True),
        ),
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='deployment_location',
            field=models.CharField(blank=True, db_comment='部署位置名称（如"北京市朝阳区森林公园"）', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='deployment_status',
            field=models.CharField(choices=[('deployed', '已部署'), ('pending', '待部署'), ('maintenance', '维护中'), ('withdrawn', '已撤回')], db_comment='部署状态', db_index=True, default='pending', max_length=20),
        ),
        migrations.AddIndex(
            model_name='ttdronebasicinfo',
            index=models.Index(fields=['deployment_status'], name='idx_drone_deployment_status'),
        ),
        migrations.AddIndex(
            model_name='ttdronebasicinfo',
            index=models.Index(fields=['deployment_location'], name='idx_drone_deployment_location'),
        ),
    ]
