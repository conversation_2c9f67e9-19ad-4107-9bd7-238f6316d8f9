#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time : 2024-12-26 当前系统时间
# <AUTHOR> zhous
# @Email : <EMAIL>
# @File : drone_views.py
# @Descr : 后台管理无人机视图
# @Software: VSCode

from rest_framework import viewsets
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from my_app.models import TtDroneBasicInfo
from my_app.serializers.admin.drone_serializers import DroneAdminSerializer
from my_app.serializers.common import CommonResponseSerializer
from my_app.views.base_views import SecureModelViewSet

class DroneAdminViewSet(SecureModelViewSet):
    """
    后台管理无人机管理API
    提供完整的无人机管理功能
    """
    queryset = TtDroneBasicInfo.objects.all().order_by('-create_time')
    serializer_class = DroneAdminSerializer

    # 配置过滤和排序
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['status', 'brand', 'deployment_status']
    ordering_fields = ['create_time', 'drone_serial', 'status']
    ordering = ['-create_time']
    
    def get_permissions(self):
        """确保只有管理员能访问"""
        permissions = super().get_permissions()
        if not (self.request.user.is_staff or self.request.user.is_superuser):
            self.permission_denied(self.request, message="需要管理员权限")
        return permissions
    
    @swagger_auto_schema(
        operation_description="获取无人机列表",
        operation_summary="无人机列表管理",
        manual_parameters=[
            openapi.Parameter('status', openapi.IN_QUERY, description="设备状态", type=openapi.TYPE_STRING),
            openapi.Parameter('brand', openapi.IN_QUERY, description="设备品牌", type=openapi.TYPE_STRING),
            openapi.Parameter('deployment_status', openapi.IN_QUERY, description="部署状态", type=openapi.TYPE_STRING),
            openapi.Parameter('ordering', openapi.IN_QUERY, description="排序字段", type=openapi.TYPE_STRING),
        ],
        responses={200: CommonResponseSerializer},
        tags=['后台管理-无人机管理']
    )
    def list(self, request, *args, **kwargs):
        """获取无人机列表"""
        response = super().list(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="无人机列表获取成功"
        ))

    @swagger_auto_schema(
        operation_description="获取无人机详情",
        operation_summary="无人机详情管理",
        responses={200: CommonResponseSerializer, 404: CommonResponseSerializer},
        tags=['后台管理-无人机管理']
    )
    def retrieve(self, request, *args, **kwargs):
        """获取无人机详情"""
        response = super().retrieve(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="无人机详情获取成功"
        ))
    
    @swagger_auto_schema(
        operation_description="创建无人机",
        operation_summary="无人机创建",
        request_body=DroneAdminSerializer,
        responses={201: CommonResponseSerializer},
        tags=['后台管理-无人机管理']
    )
    def create(self, request, *args, **kwargs):
        """创建无人机"""
        response = super().create(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="无人机创建成功"
        ), status=201)
    
    @swagger_auto_schema(
        operation_description="更新无人机信息",
        operation_summary="无人机信息更新",
        request_body=DroneAdminSerializer,
        responses={200: CommonResponseSerializer},
        tags=['后台管理-无人机管理']
    )
    def update(self, request, *args, **kwargs):
        """更新无人机信息"""
        response = super().update(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="无人机更新成功"
        ))

    @swagger_auto_schema(
        operation_description="部分更新无人机信息",
        operation_summary="无人机部分更新",
        request_body=DroneAdminSerializer,
        responses={200: CommonResponseSerializer, 400: CommonResponseSerializer},
        tags=['后台管理-无人机管理']
    )
    def partial_update(self, request, *args, **kwargs):
        """部分更新无人机信息"""
        response = super().partial_update(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="无人机部分更新成功"
        ))
    
    @swagger_auto_schema(
        operation_description="删除无人机",
        operation_summary="无人机删除",
        responses={204: CommonResponseSerializer},
        tags=['后台管理-无人机管理']
    )
    def destroy(self, request, *args, **kwargs):
        """删除无人机"""
        super().destroy(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            message="无人机删除成功"
        ), status=204)
