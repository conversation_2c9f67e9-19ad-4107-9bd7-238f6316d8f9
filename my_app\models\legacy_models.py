# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each ForeignKey and OneToOneField has `on_delete` set to the desired behavior
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
from django.db import models


class DjangoApschedulerDjangojob(models.Model):
    id = models.AutoField(db_column='ID', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='NAME', max_length=255, blank=True, null=True)  # Field name made lowercase.
    next_run_time = models.DateTimeField(db_column='NEXT_RUN_TIME', blank=True, null=True)  # Field name made lowercase.
    job_state = models.BinaryField(db_column='JOB_STATE', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'DJANGO_APSCHEDULER_DJANGOJOB'


class WjyyGzBggl(models.Model):
    id = models.BigIntegerField()
    mc = models.CharField(max_length=255, blank=True, null=True)
    baogao = models.TextField(blank=True, null=True)  # This field type is a guess.
    create_time = models.DateTimeField(blank=True, null=True)
    create_user = models.CharField(max_length=255, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    bg_type = models.BigIntegerField(blank=True, null=True, db_comment='1：地理环境分析，\r\n2：综合保障分析，\r\n3：重要目标分析，\r\n4：集结地域分析，\r\n5：观察位置分析，\r\n6：外围封控分析，\r\n7：驱散方向分析，\r\n8：综合分析')

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_BGGL'
        db_table_comment = '报告管理'


class WjyyGzSpatialRefSys(models.Model):
    srid = models.IntegerField()
    auth_name = models.CharField(max_length=256, blank=True, null=True)
    auth_srid = models.IntegerField(blank=True, null=True)
    srtext = models.CharField(max_length=2048, blank=True, null=True)
    proj4text = models.CharField(max_length=2048, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SPATIAL_REF_SYS'


class WjyyGzSysConfig(models.Model):
    id = models.BigAutoField()
    param_key = models.CharField(max_length=50, blank=True, null=True)
    param_value = models.CharField(max_length=2000, blank=True, null=True)
    status = models.IntegerField(blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_CONFIG'
        db_table_comment = '系统配置表'


class WjyyGzSysDepartment(models.Model):
    department_id = models.BigIntegerField(blank=True, null=True, db_comment='部门id')
    department_name = models.CharField(max_length=128, blank=True, null=True, db_comment='部门名称')
    parent_id = models.BigIntegerField(blank=True, null=True, db_comment='上级部门id')
    state = models.CharField(max_length=1, blank=True, null=True, db_comment='状态(1正常，0停用)')
    state_date = models.DateField(blank=True, null=True, db_comment='状态时间')
    order_num = models.BigIntegerField(blank=True, null=True, db_comment='部门顺序')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建者id')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    del_flag = models.IntegerField(blank=True, null=True, db_comment='是否删除(0否，1是)')
    master = models.CharField(max_length=255, blank=True, null=True, db_comment='部门负责人')
    tel = models.CharField(max_length=255, blank=True, null=True, db_comment='负责人电话')
    email = models.CharField(max_length=255, blank=True, null=True, db_comment='邮箱')

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_DEPARTMENT'
        db_table_comment = '系统部门表'


class WjyyGzSysLog(models.Model):
    id = models.BigAutoField()
    username = models.CharField(max_length=50, blank=True, null=True)
    operation = models.CharField(max_length=255, blank=True, null=True)
    method = models.CharField(max_length=200, blank=True, null=True)
    params = models.CharField(max_length=5000, blank=True, null=True)
    time = models.FloatField()
    ip = models.CharField(max_length=64, blank=True, null=True)
    create_date = models.DateTimeField(blank=True, null=True)
    error_info = models.TextField(blank=True, null=True)
    log_type = models.BigIntegerField(blank=True, null=True, db_comment='日志类型：0:登录日志，1：操作日志，2：异常日志，3：警告日志')

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_LOG'
        db_table_comment = '系统日志表'


class WjyyGzSysMenu(models.Model):
    menu_id = models.BigAutoField()
    parent_id = models.BigIntegerField(blank=True, null=True)
    name = models.CharField(max_length=50, blank=True, null=True)
    url = models.CharField(max_length=200, blank=True, null=True)
    perms = models.CharField(max_length=500, blank=True, null=True)
    type = models.IntegerField(blank=True, null=True, db_comment='1为菜单')
    icon = models.CharField(max_length=50, blank=True, null=True)
    order_num = models.IntegerField(blank=True, null=True)
    is_show = models.CharField(max_length=1, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_MENU'
        db_table_comment = '系统菜单表'


class WjyyGzSysOss(models.Model):
    id = models.BigAutoField()
    url = models.CharField(max_length=200, blank=True, null=True)
    create_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_OSS'
        db_table_comment = '系统存储服务表'


class WjyyGzSysParam(models.Model):
    id = models.BigAutoField()
    param_en_key = models.CharField(max_length=200, blank=True, null=True, db_comment='参数键英文名称')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    param_value = models.CharField(max_length=255, blank=True, null=True, db_comment='参数键值')
    param_cn_key = models.CharField(max_length=200, blank=True, null=True, db_comment='参数键中文名称')
    update_time = models.DateTimeField(blank=True, null=True, db_comment='更新时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建者ID')
    update_user_id = models.BigIntegerField(blank=True, null=True, db_comment='更新者ID')

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_PARAM'
        db_table_comment = '系统参数表'


class WjyyGzSysRole(models.Model):
    role_id = models.BigAutoField()
    role_name = models.CharField(max_length=100, blank=True, null=True)
    remark = models.CharField(max_length=100, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_ROLE'
        db_table_comment = '系统角色表'


class WjyyGzSysRoleMenu(models.Model):
    id = models.BigAutoField()
    role_id = models.BigIntegerField(blank=True, null=True)
    menu_id = models.BigIntegerField(blank=True, null=True)
    perms = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_ROLE_MENU'
        db_table_comment = '系统角色菜单表'


class WjyyGzSysUserRole(models.Model):
    id = models.BigAutoField()
    user_id = models.BigIntegerField(blank=True, null=True)
    role_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_USER_ROLE'
        db_table_comment = '系统用户角色表'


class WjyyGzSysUserToken(models.Model):
    user_id = models.BigAutoField()
    token = models.CharField(max_length=100)
    expire_time = models.DateTimeField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_SYS_USER_TOKEN'
        db_table_comment = '用户tokens表（弃用）'


class WjyyGzTmDistrict(models.Model):
    id = models.BigAutoField()
    dis_name = models.CharField(max_length=255, blank=True, null=True)
    dis_code = models.IntegerField(blank=True, null=True)
    parent_code = models.IntegerField(blank=True, null=True)
    type = models.SmallIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_TM_DISTRICT'
        db_table_comment = '行政区划表'


class WjyyGzTtUploadFileData(models.Model):
    id = models.BigAutoField(db_comment='业务主键id')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建者id')
    file_size = models.DecimalField(max_digits=24, decimal_places=0, blank=True, null=True, db_comment='文件大小')
    file_name = models.CharField(max_length=255, blank=True, null=True, db_comment='文件名称')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    file_suffix = models.CharField(max_length=255, blank=True, null=True, db_comment='文件后缀')
    path = models.CharField(max_length=512, blank=True, null=True, db_comment='文件路径')
    has_supert_kkfileview = models.SmallIntegerField(blank=True, null=True, db_comment='kkfileview 是否支持预览：0支持，1不支持')

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_TT_UPLOAD_FILE_DATA'
        db_table_comment = '上传文件表'


class WjyyGzUsGaz(models.Model):
    id = models.AutoField()
    seq = models.IntegerField(blank=True, null=True)
    word = models.TextField(blank=True, null=True)
    stdword = models.TextField(blank=True, null=True)
    token = models.IntegerField(blank=True, null=True)
    is_custom = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_US_GAZ'


class WjyyGzUsLex(models.Model):
    id = models.AutoField()
    seq = models.IntegerField(blank=True, null=True)
    word = models.TextField(blank=True, null=True)
    stdword = models.TextField(blank=True, null=True)
    token = models.IntegerField(blank=True, null=True)
    is_custom = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_US_LEX'


class WjyyGzUsRules(models.Model):
    id = models.AutoField()
    rule = models.TextField(blank=True, null=True)
    is_custom = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'WJYY_GZ_US_RULES'


class AuthGroup(models.Model):
    id = models.AutoField()
    name = models.CharField(max_length=150)

    class Meta:
        managed = False
        db_table = 'auth_group'


class AuthGroupPermissions(models.Model):
    id = models.AutoField()
    group_id = models.IntegerField()
    permission_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'auth_group_permissions'


class AuthPermission(models.Model):
    id = models.AutoField()
    name = models.CharField(max_length=255)
    content_type_id = models.IntegerField()
    codename = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'auth_permission'


class AuthUser(models.Model):
    id = models.BigIntegerField(db_comment='id')
    password = models.CharField(max_length=128, db_comment='用户密码')
    last_login = models.DateTimeField(blank=True, null=True, db_comment='上次登录时间')
    is_superuser = models.BooleanField(db_comment='是否是超级用户')
    username = models.CharField(max_length=150, blank=True, null=True, db_comment='完整姓名')
    first_name = models.CharField(max_length=150, blank=True, null=True, db_comment='名字')
    last_name = models.CharField(max_length=150, blank=True, null=True, db_comment='姓氏')
    email = models.CharField(max_length=254, blank=True, null=True, db_comment='邮箱')
    is_staff = models.BooleanField(blank=True, null=True, db_comment='是否是职员')
    is_active = models.BooleanField(blank=True, null=True, db_comment='账户是否激活')
    date_joined = models.DateTimeField(blank=True, null=True, db_comment='账户创建时间')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建者用户id')
    department_id = models.BigIntegerField(blank=True, null=True, db_comment='部门id')
    fullname = models.CharField(max_length=255, blank=True, null=True, db_comment='完整姓名')
    login_error_attempts = models.SmallIntegerField(blank=True, null=True, db_comment='登录错误次数')
    login_locked_until = models.DateTimeField(blank=True, null=True, db_comment='登录锁定时间')
    mobile = models.CharField(max_length=100, blank=True, null=True, db_comment='电话')
    modify_time = models.DateTimeField(blank=True, null=True, db_comment='修改时间')
    modify_user_id = models.BigIntegerField(blank=True, null=True, db_comment='修改者用户id')
    sex = models.CharField(max_length=255, blank=True, null=True, db_comment='性别')
    status = models.IntegerField(blank=True, null=True, db_comment='状态，1正常，0 禁用')
    age = models.IntegerField(blank=True, null=True, db_comment='年龄')
    educational_background = models.CharField(max_length=100, blank=True, null=True, db_comment='教育背景')
    ethnicity = models.CharField(max_length=100, blank=True, null=True, db_comment='所属民族')
    id_card = models.CharField(max_length=20, blank=True, null=True, db_comment='身份证号码')
    template_id = models.BigIntegerField(blank=True, null=True, db_comment='当前用户所使用的模板id')

    class Meta:
        managed = False
        db_table = 'auth_user'
        db_table_comment = '系统用户表'


class AuthUserGroups(models.Model):
    name = models.CharField(max_length=150)
    wjyygzauthuser_id = models.IntegerField(blank=True, null=True)
    group_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'auth_user_groups'


class AuthUserPermissions(models.Model):
    wjyygzauthuser_id = models.IntegerField()
    permission_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'auth_user_permissions'


class AuthtokenToken(models.Model):
    key = models.CharField(primary_key=True, max_length=40)
    created = models.DateTimeField()
    user_id = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'authtoken_token'
        db_table_comment = '用户tokens表'


class DjangoAdminLog(models.Model):
    id = models.AutoField()
    action_time = models.DateTimeField()
    object_id = models.TextField(blank=True, null=True)
    object_repr = models.CharField(max_length=200)
    action_flag = models.SmallIntegerField()
    change_message = models.TextField()
    content_type_id = models.IntegerField(blank=True, null=True)
    user_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'django_admin_log'


class DjangoApschedulerDjangojob(models.Model):
    id = models.CharField(max_length=255)
    next_run_time = models.DateTimeField(blank=True, null=True)
    job_state = models.BinaryField()

    class Meta:
        managed = False
        db_table = 'django_apscheduler_djangojob'
        db_table_comment = '任务计划表'


class DjangoApschedulerDjangojobexecution(models.Model):
    id = models.BigAutoField()
    status = models.CharField(max_length=50)
    run_time = models.DateTimeField()
    duration = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    finished = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    exception = models.CharField(max_length=1000, blank=True, null=True)
    traceback = models.TextField(blank=True, null=True)
    job_id = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = 'django_apscheduler_djangojobexecution'
        db_table_comment = '任务计划执行情况表'


class DjangoContentType(models.Model):
    id = models.AutoField()
    app_label = models.CharField(max_length=100)
    model = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'django_content_type'


class DjangoMigrations(models.Model):
    id = models.AutoField()
    app = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    applied = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'django_migrations'


class DjangoSession(models.Model):
    session_key = models.CharField(max_length=40)
    session_data = models.TextField()
    expire_date = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'django_session'
        db_table_comment = 'django-session表'


class SpatialRefSys(models.Model):
    srid = models.IntegerField(primary_key=True)
    auth_name = models.CharField(max_length=256, blank=True, null=True)
    auth_srid = models.IntegerField(blank=True, null=True)
    srtext = models.CharField(max_length=2048, blank=True, null=True)
    proj4text = models.CharField(max_length=2048, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'spatial_ref_sys'


class TtDronAlertFileData(models.Model):
    id = models.BigIntegerField(db_comment='业务主键id')
    alert_id = models.BigIntegerField()
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建者id')
    file_size = models.DecimalField(max_digits=24, decimal_places=0, blank=True, null=True, db_comment='文件大小')
    file_name = models.CharField(max_length=255, blank=True, null=True, db_comment='文件名称')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    file_suffix = models.CharField(max_length=255, blank=True, null=True, db_comment='文件后缀')
    path = models.CharField(max_length=512, blank=True, null=True, db_comment='文件路径')
    has_supert_kkfileview = models.SmallIntegerField(blank=True, null=True, db_comment='kkfileview 是否支持预览：0支持，1不支持')

    class Meta:
        managed = False
        db_table = 'tt_dron_alert_file_data'
        db_table_comment = '告警图片视频表'


class TtDronBaskFileData(models.Model):
    id = models.BigIntegerField(db_comment='业务主键id')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建者id')
    file_size = models.DecimalField(max_digits=24, decimal_places=0, blank=True, null=True, db_comment='文件大小')
    file_name = models.CharField(max_length=255, blank=True, null=True, db_comment='文件名称')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    file_suffix = models.CharField(max_length=255, blank=True, null=True, db_comment='文件后缀')
    path = models.CharField(max_length=512, blank=True, null=True, db_comment='文件路径')
    has_supert_kkfileview = models.SmallIntegerField(blank=True, null=True, db_comment='kkfileview 是否支持预览：0支持，1不支持')

    class Meta:
        managed = False
        db_table = 'tt_dron_bask_file_data'
        db_table_comment = '任务缩略图上传文件表'


class TtDroneAlerts(models.Model):
    alert_id = models.BigAutoField(primary_key=True, db_comment='告警记录唯一标识')
    alert_time = models.DateTimeField(blank=True, null=True, db_comment='告警发生时间（带时区）')
    alert_location = models.CharField(max_length=255, blank=True, null=True, db_comment='告警发生位置')
    notes = models.CharField(max_length=255, blank=True, null=True, db_comment='告警发生描述')
    task_id = models.BigIntegerField(blank=True, null=True, db_comment='任务id')
    flight_id = models.BigIntegerField(blank=True, null=True, db_comment='飞行记录id')
    drone_type = models.CharField(max_length=50, blank=True, null=True)
    alert_type_id = models.CharField(max_length=50, blank=True, null=True, db_comment='告警类型（如：禁飞区闯入、异常飞行等）')
    alert_level = models.SmallIntegerField(blank=True, null=True, db_comment='告警级别（1-5级，1为最低，10为最高）')
    points = models.CharField(max_length=255, blank=True, null=True, db_comment='警告点')
    areas = models.CharField(max_length=2000, blank=True, null=True, db_comment='警告面')
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tt_drone_alerts'
        db_table_comment = '告警记录'


class TtDroneBasicInfo(models.Model):
    drone_id = models.BigIntegerField(db_comment='无人机唯一标识符，自增主键')
    drone_serial = models.CharField(unique=True, max_length=50, db_comment='无人机序列号，设备出厂唯一编号')
    model = models.CharField(max_length=50, blank=True, null=True, db_comment='无人机型号（如 Mavic 3、Phantom 4）')
    brand = models.CharField(max_length=50, blank=True, null=True, db_comment='无人机品牌（如 DJI、Autel、Parrot）')
    weight_kg = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True, db_comment='无人机重量（千克）')
    dimensions = models.CharField(max_length=100, blank=True, null=True, db_comment='无人机尺寸（展开/折叠状态）')
    max_flight_time_min = models.IntegerField(blank=True, null=True, db_comment='最大续航时间（分钟）')
    camera_model = models.CharField(max_length=100, blank=True, null=True, db_comment='搭载的相机型号')
    camera_resolution = models.CharField(max_length=50, blank=True, null=True, db_comment='相机分辨率（如 4K/60fps）')
    gps_precision = models.CharField(max_length=50, blank=True, null=True, db_comment='GPS定位精度（如厘米级/米级）')
    obstacle_avoidance = models.BooleanField(blank=True, null=True, db_comment='是否支持避障功能')
    status = models.CharField(max_length=20, blank=True, null=True, db_comment='设备状态：available(可用)、maintenance(维修中)、retired(报废)')
    battery_capacity_mah = models.IntegerField(blank=True, null=True, db_comment='电池容量（毫安时）')
    firmware_version = models.CharField(max_length=20, blank=True, null=True, db_comment='当前固件版本号')
    department = models.CharField(max_length=50, blank=True, null=True, db_comment='所属部门/团队')
    responsible_person = models.CharField(max_length=50, blank=True, null=True, db_comment='设备负责人')
    purchase_date = models.DateField(blank=True, null=True, db_comment='采购日期')
    purchase_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, db_comment='采购价格（元）')
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tt_drone_basic_info'
        db_table_comment = '无人机设备基础信息表'


class TtDroneFlightRecords(models.Model):
    flight_id = models.BigIntegerField(db_comment='飞行记录唯一标识符，自增主键')
    drone_id = models.BigIntegerField(db_comment='执行飞行的无人机ID，关联drone_basic_info表')
    flight_number = models.CharField(unique=True, max_length=50, db_comment='飞行编号（格式：DRN-YYYYMMDD-XXXX）')
    task_id = models.IntegerField(db_comment='关联的任务ID（可关联任务表）')
    start_time = models.DateTimeField(blank=True, null=True, db_comment='飞行开始时间（UTC+8）')
    end_time = models.DateTimeField(blank=True, null=True, db_comment='飞行结束时间（UTC+8）')
    total_flight_time_sec = models.IntegerField(blank=True, null=True, db_comment='总飞行时长（秒）')
    start_location = models.TextField(blank=True, null=True, db_comment='起飞位置（WGS84坐标系，格式：POINT(longitude latitude)）')  # This field type is a guess.
    end_location = models.TextField(blank=True, null=True, db_comment='降落位置（WGS84坐标系，格式：POINT(longitude latitude)）')  # This field type is a guess.
    max_altitude_m = models.DecimalField(max_digits=6, decimal_places=2, blank=True, null=True, db_comment='最大飞行高度（米）')
    avg_speed_kmh = models.DecimalField(max_digits=6, decimal_places=2, blank=True, null=True, db_comment='平均飞行速度（千米/小时）')
    weather_conditions = models.JSONField(blank=True, null=True, db_comment='飞行时天气状况（JSON格式：温度、湿度、风速等）')
    flight_path = models.TextField(blank=True, null=True, db_comment='完整飞行轨迹（WGS84坐标系，LINESTRING格式）')  # This field type is a guess.
    telemetry_data = models.JSONField(blank=True, null=True, db_comment='实时飞行数据（JSON格式：经纬度、高度、速度等）')
    device_status = models.JSONField(blank=True, null=True, db_comment='设备状态数据（JSON格式：电池电量、电机温度等）')
    operator_id = models.IntegerField(blank=True, null=True, db_comment='操控员ID（可关联用户表）')
    flight_mode = models.CharField(max_length=20, blank=True, null=True, db_comment='飞行模式：auto(自动)、manual(手动)、waypoint(航点)')
    mission_success = models.BooleanField(blank=True, null=True, db_comment='任务是否成功完成')
    notes = models.TextField(blank=True, null=True, db_comment='飞行备注信息')
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    is_del = models.SmallIntegerField(blank=True, null=True, db_comment='0 未删除 1已删除')

    class Meta:
        managed = False
        db_table = 'tt_drone_flight_records'
        db_table_comment = '无人机飞行记录表'


class TtDroneFlyXydata(models.Model):
    xy_id = models.BigIntegerField(db_comment=' 自增主键，唯一标识每条轨迹记录 ')
    create_time = models.DateTimeField(blank=True, null=True, db_comment=' 记录创建时间，自动填充服务器当前时间 ')
    drone_id = models.BigIntegerField(blank=True, null=True, db_comment=' 无人机唯一标识符，如设备 SN 号或自定义编号 ')
    flight_id = models.BigIntegerField(blank=True, null=True, db_comment=' 飞行会话 ID，用于关联同一次飞行任务的所有记录 ')
    chuizgd = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, db_comment=' 无人机距离地面的垂直高度，单位米 ')
    licgd = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, db_comment=' 无人机距离起飞点（巢）的垂直高度，单位米 ')
    lic_jul = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, db_comment=' 无人机距离起飞点（巢）的水平直线距离，单位米 ')
    jitou_jd = models.DecimalField(max_digits=6, decimal_places=2, blank=True, null=True, db_comment=' 机头朝向角度，以正北为 0°，顺时针计算 ')
    upload_xinh = models.IntegerField(blank=True, null=True, db_comment=' 数据上传信号强度，范围 0-100，100 表示最强 ')
    download_xinh = models.IntegerField(blank=True, null=True, db_comment=' 数据下载信号强度，范围 0-100，100 表示最强 ')
    start_count = models.IntegerField(blank=True, null=True, db_comment=' 当前连接的卫星数量，影响定位精度 ')
    luopan_status = models.CharField(max_length=20, blank=True, null=True, db_comment=' 罗盘工作状态，正常时为 "工作中"')
    xinhao_ganrao = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, db_comment=' 无线信道干扰强度，单位分贝毫瓦，0 表示无干扰 ')
    shitu_zhil = models.CharField(max_length=20, blank=True, null=True, db_comment=' 图传信号状态，反映实时视频传输质量 ')
    flight_mode = models.CharField(max_length=20, blank=True, null=True, db_comment=' 飞行模式，如手动控制、自动巡航、返航等 ')
    shuip_sud = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, db_comment=' 水平方向移动速度，单位米 / 秒 ')
    chuiz_sud = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, db_comment=' 垂直方向移动速度，单位米 / 秒，正值上升，负值下降 ')
    dianc_wd = models.DecimalField(max_digits=6, decimal_places=2, blank=True, null=True, db_comment=' 电池当前温度，单位摄氏度，过高需警惕 ')
    latitude = models.DecimalField(max_digits=10, decimal_places=7, blank=True, null=True, db_comment=' 无人机当前纬度坐标，WGS84 格式，可用于轨迹地图展示 ')
    longitude = models.DecimalField(max_digits=10, decimal_places=7, blank=True, null=True, db_comment=' 无人机当前经度坐标，WGS84 格式，可用于轨迹地图展示 ')
    notes = models.TextField(blank=True, null=True, db_comment=' 附加备注信息，如操作记录、异常情况说明等 ')
    create_user_id = models.BigIntegerField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    task_id = models.BigIntegerField(blank=True, null=True, db_comment='任务id')

    class Meta:
        managed = False
        db_table = 'tt_drone_fly_xydata'
        db_table_comment = '无人机坐标记录'


class TtDroneTasks(models.Model):
    id = models.BigIntegerField()
    task_name = models.CharField(max_length=255, blank=True, null=True, db_comment='任务的唯一名称，用于标识和检索任务')
    task_status = models.CharField(max_length=255, blank=True, null=True, db_comment='任务状态')
    task_shape = models.CharField(max_length=50, blank=True, null=True, db_comment='任务区域的几何形状类型')
    perimeter = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, db_comment='任务区域的周长，单位：米')
    area = models.DecimalField(max_digits=10, decimal_places=4, blank=True, null=True, db_comment='任务区域的面积，单位：平方千米')
    center_longitude = models.DecimalField(max_digits=9, decimal_places=6, blank=True, null=True, db_comment='任务区域的中心点经度，WGS84坐标系')
    center_latitude = models.DecimalField(max_digits=9, decimal_places=6, blank=True, null=True, db_comment='任务区域的中心点纬度，WGS84坐标系')
    location_detail = models.TextField(blank=True, null=True, db_comment='任务位置的详细描述信息')
    task_purpose = models.CharField(max_length=50, blank=True, null=True, db_comment='任务的主要用途类型')
    task_urgency = models.CharField(max_length=50, blank=True, null=True, db_comment='任务的紧急程度级别')
    remark = models.CharField(max_length=200, blank=True, null=True, db_comment='任务的附加说明信息，最大长度200字符')
    selected_drone_id = models.CharField(max_length=50, blank=True, null=True, db_comment='执行此任务的无人机唯一标识符')
    task_shape_points = models.CharField(max_length=900, blank=True, null=True, db_comment='区域点数据')
    thumbnail = models.CharField(max_length=900, blank=True, null=True, db_comment='任务缩略图 image url')
    mark_count = models.IntegerField(blank=True, null=True, db_comment='标记数量')
    shenh = models.IntegerField(blank=True, null=True, db_comment='审核 0待审核 1通过 2未通过')
    photo_count = models.IntegerField(blank=True, null=True, db_comment='照片数量')
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user_id = models.BigIntegerField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tt_drone_tasks'
        db_table_comment = '任务表'


class TtSelectDict(models.Model):
    id = models.BigIntegerField()
    ename = models.CharField(max_length=100, blank=True, null=True)
    cname = models.CharField(max_length=100, blank=True, null=True)
    type = models.CharField(max_length=100, blank=True, null=True)
    remark = models.CharField(max_length=255, blank=True, null=True)
    create_user_id = models.BigIntegerField(blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    modify_user_id = models.BigIntegerField(blank=True, null=True)
    modify_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tt_select_dict'
        db_table_comment = '下拉属性字典'


class TtTaskSltFileData(models.Model):
    id = models.BigAutoField(db_comment='业务主键id')
    create_user_id = models.BigIntegerField(blank=True, null=True, db_comment='创建者id')
    file_size = models.DecimalField(max_digits=24, decimal_places=0, blank=True, null=True, db_comment='文件大小')
    file_name = models.CharField(max_length=255, blank=True, null=True, db_comment='文件名称')
    create_time = models.DateTimeField(blank=True, null=True, db_comment='创建时间')
    file_suffix = models.CharField(max_length=255, blank=True, null=True, db_comment='文件后缀')
    path = models.CharField(max_length=512, blank=True, null=True, db_comment='文件路径')
    has_supert_kkfileview = models.SmallIntegerField(blank=True, null=True, db_comment='kkfileview 是否支持预览：0支持，1不支持')

    class Meta:
        managed = False
        db_table = 'tt_task_slt_file_data'
        db_table_comment = '任务缩略图上传文件表'


class TtWeatherData(models.Model):
    id = models.BigIntegerField()
    wendu = models.CharField(max_length=50, blank=True, null=True)
    xiangduishidu = models.CharField(max_length=50, blank=True, null=True)
    qiya = models.CharField(max_length=50, blank=True, null=True)
    fengsu = models.CharField(max_length=50, blank=True, null=True)
    fengxiang = models.CharField(max_length=50, blank=True, null=True)
    jiangshui = models.CharField(max_length=50, blank=True, null=True)
    tianqi = models.CharField(max_length=50, blank=True, null=True)
    nengjiandu = models.DecimalField(max_digits=7, decimal_places=2, blank=True, null=True)
    yunliang = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    ludianwendu = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    uvzhishu = models.DecimalField(max_digits=4, decimal_places=1, blank=True, null=True)
    created_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tt_weather_data'
