#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
# @Time    : 2022/5/7 10:01
# <AUTHOR> gisfan_ai
# @Email   : <EMAIL>
# @File    : userManager.py
# @Desc    ：置信度系统的业务处理类
# @Software: PyCharm

import io
import logging
import random
from datetime import timedelta

from PIL import Image, ImageDraw, ImageFont
from django.http import HttpResponse
from django.utils import timezone
from loguru import logger
from vgis_log.logTools import LoggerHelper
from vgis_utils.vgis_datetime.datetimeTools import DateTimeHelper

# 用户相关操作类
from my_app.apps import MyAppConfig
from my_app.models import AuthUser, SysLog, SysParam
from my_app.serializers import AuthUserpulldownSerializer
from my_app.utils.sysmanUtility import SysmanHelper
from my_app.views.response.baseRespone import Result
from my_app.utils.numberUtility import NumberHelper

logger = logging.getLogger('django')


class UserOperator:
    def __init__(self, connection):
        self.connection = connection

    def get_LOGIN_LOCKED_TIME(self):
        """
        获取登录锁定时间的配置值。
        从系统参数表中查询 `LOGIN_LOCKED_TIME` 参数的值，如果查询到则转换为整数返回，
        若未查询到则返回默认值 600 秒。

        :return: 登录锁定时间（秒）
        """
        # 从系统参数表中查询 `LOGIN_LOCKED_TIME` 参数的对象
        obj = SysParam.objects.get(param_en_key='LOGIN_LOCKED_TIME')
        if obj is not None:
            # 如果查询到对象，将参数值转换为整数并返回
            return int(obj.param_value)
        else:
            # 若未查询到对象，返回默认的登录锁定时间 600 秒
            return 600

    def get_LOGIN_ERROR_ATTEMPTS(self):
        """
        获取登录错误尝试次数的配置值。
        从系统参数表中查询 `LOGIN_ERROR_ATTEMPTS` 参数的值，如果查询到则转换为整数返回，
        若未查询到则返回默认值 4 次。

        :return: 登录错误尝试次数
        """
        # 从系统参数表中查询 `LOGIN_ERROR_ATTEMPTS` 参数的对象
        obj = SysParam.objects.get(param_en_key='LOGIN_ERROR_ATTEMPTS')
        if obj is not None:
            # 如果查询到对象，将参数值转换为整数并返回
            return int(obj.param_value)
        else:
            # 若未查询到对象，返回默认的登录错误尝试次数 4 次
            return 4

    def return_is_use_verification_code(self, request):
        """
        返回是否使用验证码的结果。
        调用 `get_is_use_verification_code` 方法获取是否使用验证码的布尔值，
        并将其封装到响应字典中返回。

        :param request: 请求对象
        :return: 包含是否使用验证码结果的响应字典
        """
        res = {
            'success': True,
            'code': 1,
            # 调用 `get_is_use_verification_code` 方法获取是否使用验证码的布尔值
            'value': self.get_is_use_verification_code()
        }
        return res

    def get_is_use_verification_code(self):
        """
        获取是否使用验证码的配置值。
        从系统参数表中查询 `IS_USE_VERIFICATION_CODE` 参数的值，如果查询到且值为 "是" 则返回 `True`，
        否则返回 `False`。

        :return: 是否使用验证码的布尔值
        """
        # 从系统参数表中查询 `IS_USE_VERIFICATION_CODE` 参数的对象
        obj = SysParam.objects.get(param_en_key='IS_USE_VERIFICATION_CODE')
        if obj is not None:
            # 如果查询到对象，判断参数值是否为 "是"，是则返回 `True`，否则返回 `False`
            return True if obj.param_value == "是" else False
        else:
            # 若未查询到对象，返回 `False`，表示不使用验证码
            return False

    # 登录
    # 通过用户名和密码登录
    # 连续输错4次密码，锁定10分钟，10分钟后没输错一次密码都重新锁定10分钟---参数可配置
    def login(self, request, username, password, verifcation, auth, Token):
        """
        用户登录功能。
        根据用户名和密码进行登录验证，处理登录失败次数和账号锁定逻辑，
        支持验证码验证，登录成功后创建新的 Token。

        :param request: 请求对象
        :param username: 用户名
        :param password: 密码
        :param verifcation: 验证码
        :param auth: 认证对象
        :param Token: Token 模型类
        :return: 包含登录结果的响应字典
        """
        function_title = "用户登录"
        try:
            # 记录日志开始信息
            start = LoggerHelper.set_start_log_info(logger)
            # 根据用户名获取用户对象
            userObject = AuthUser.objects.get(username=username)
            if userObject.status == 0:

                error_message = "账号已被锁定，请联系管理员。"
                res = {
                    'success': False,
                    'code': -1,
                    'message': error_message
                }
                return res
            # 账号被锁
            if userObject.login_locked_until and userObject.login_locked_until > timezone.now():
                # 计算账号剩余锁定时间
                remaining_time = (userObject.login_locked_until - timezone.now()).total_seconds()
                # 构造账号被锁定的错误信息
                error_message = "账号已被锁定，请在{}后重试。".format(DateTimeHelper.convert_seconds(remaining_time))
                res = {
                    'success': False,
                    'code': -1,
                    'message': error_message
                }
                return res
            # 账号没有被锁
            else:
                # userObj = AuthUser.objects.get(username=username)
                print("当前用户 status:", userObject.status)
                res = {
                    'success': False,
                    'code': -1,
                    'message': '用户未通过审核!！'
                }
                if userObject.status == 2:
                    return res
                # 使用认证对象进行用户名和密码的验证
                user = auth.authenticate(username=username, password=password)
                # 登录失败
                if not user:
                    # 登录失败，增加失败次数
                    if userObject.login_error_attempts is None:
                        userObject.login_error_attempts = 1
                    else:
                        userObject.login_error_attempts += 1
                    # 判断登录失败次数是否达到配置的最大尝试次数
                    if userObject.login_error_attempts >= self.get_LOGIN_ERROR_ATTEMPTS():
                        # 锁定账号，设置锁定结束时间
                        userObject.login_locked_until = timezone.now() + timedelta(seconds=self.get_LOGIN_LOCKED_TIME())
                    # 保存用户对象的修改
                    userObject.save()
                    res = {
                        'success': False,
                        'code': -1,
                        'message': '用户名或密码不对!'
                    }
                    return res
                else:
                    # 验证码
                    # 注意前端的使用，可能因为代理的范围，造成前端访问的session保存有问题，造成验证码匹配不了，无法访问session
                    if self.get_is_use_verification_code():
                        try:
                            # 验证输入的验证码是否与 session 中的验证码一致
                            if verifcation != request.session['code']:
                                res = {
                                    'success': False,
                                    'code': -1,
                                    'message': '验证码不对!'
                                }
                                return res
                        except Exception as ex:
                            res = {
                                'success': False,
                                'code': -1,
                                'message': '验证码匹配有问题!'
                            }
                            return res
                    # 登录成功，重置失败次数并解锁账号
                    userObject.login_error_attempts = 0
                    userObject.login_locked_until = None
                    # 保存用户对象的修改
                    userObject.save()
            # 判断登录成功的用户是否为有效用户
            if user.is_active:
                # 用户登录
                auth.login(request, user)
                # 删除原有的 Token
                old_token = Token.objects.filter(user=user)
                old_token.delete()
                # 创建新的 Token
                token = Token.objects.create(user=user)
                res = {
                    'success': True,
                    'code': 0,
                    'info': "{}成功！".format(function_title),
                    "userid": user.id,
                    "username": user.username,
                    "token": token.key
                }
                return res
            else:
                res = {
                    'success': False,
                    'code': -1,
                    'message': '用户被禁用！',
                    "userid": user.id,
                    "username": user.username
                }
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, username, request, function_title)
        except Exception as exp:
            # 记录异常日志信息
            res = LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, request.path, username,
                                                             request,
                                                             function_title, None, exp)
        finally:
            return res

    # 获取用户详情
    def get_user_details(self, request, user_id):
        """
        获取用户详情信息，包括用户基本信息、角色信息和菜单权限信息。

        :param request: 请求对象
        :param user_id: 用户 ID
        :return: 包含用户详情信息的响应字典
        """
        function_title = "获取用户详情"
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        res = ""
        try:
            user_info = {}
            # 获取获取用户姓名，部门
            sql = 'select tablea.username,tablea.fullname,tableb.department_name from "auth_user" tablea '
            sql += ' left join "WJYY_GZ_SYS_DEPARTMENT" tableb on tablea.department_id=tableb.department_id'
            sql += " where tablea.id={}".format(user_id)
            # 获取数据库连接的游标
            cursor = self.connection.cursor()
            # 执行 SQL 查询
            cursor.execute(sql)
            # 获取查询结果的第一行
            record = cursor.fetchone()
            if record is not None:
                # 将用户基本信息添加到用户信息字典中
                user_info["userid"] = user_id
                user_info["username"] = record[0]
                user_info["fullname"] = record[1]
                user_info["department_name"] = record[2]

            # --获取用户的角色（多个）
            sql = 'select distinct tablec.role_name, tablec.role_id from "WJYY_GZ_SYS_ROLE" tablec '
            sql += ' left join  "WJYY_GZ_SYS_USER_ROLE" tabled on tablec.role_id = tabled.role_id'
            sql += " where tabled.user_id ={}".format(user_id)
            # 执行 SQL 查询
            cursor.execute(sql)
            # 获取查询结果的所有行
            records = cursor.fetchall()
            role_list = []
            role_ids = []
            for record in records:
                role_info = {}
                # 将角色信息添加到角色信息字典中
                role_info["role_name"] = record[0]
                role_info["role_id"] = record[1]
                role_list.append(role_info)
                role_ids.append(int(record[1]))
            user_info["role_list"] = role_list

            # --根据角色获取可访问数据权限和菜单权限
            data_list = []
            menu_list = []
            if len(role_ids) > 0:
                sql = "select distinct tablee.menu_id,tablee.parent_id, tablee.name, tablee.url,tablee.type,tablee.icon,tablee.order_num,tablee.is_show"
                sql += ' from "WJYY_GZ_SYS_MENU" tablee'
                sql += ' left join "WJYY_GZ_SYS_ROLE_MENU" tablef on tablee.menu_id = tablef.menu_id'
                sql += " where tablef.role_id in ({})".format(','.join([str(i) for i in role_ids]))
                sql += " and tablee.is_show='Y'"
                # sql += " and (tablee.icon='data' or  tablee.icon='menu')"
                sql += " order by tablee.order_num"
                # 执行 SQL 查询
                cursor.execute(sql)
                # 获取查询结果的所有行
                records = cursor.fetchall()
                menu_id_list = []
                for record in records:
                    if record[0] not in menu_id_list:
                        menu_id_list.append(record[0])
                        # if record[2] == "data":
                        #     data_list.append({"data_type": record[1]})
                        # if record[2] == "menu":
                        # 将菜单信息添加到菜单信息列表中
                        menu_list.append(
                            {"menu_id": record[0], "parent_id": record[1], "name": record[2], "url": record[3],
                             "type": record[4], "icon": record[5], "order_num": record[6],
                             "is_show": record[7]})
            # user_info["data_list"] = data_list

            user_info["menu_list"] = NumberHelper.build_menu_tree(menu_list)

            res = {
                'success': True,
                'message': user_info
            }
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user,
                                          request,
                                          function_title,1)

        except Exception as exp:
            # 记录异常日志信息
            res = {
                'success': False,
                'message': "日志记录异常{}".format(exp)
            }
            LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, request.path,
                                                             request.auth.user, request,
                                                             function_title, None, exp,2)
        finally:
            return res

    # 获取用户列表-sql

    def sql_search(self, request, username, fullname, department_id):
        """
        根据条件查询用户列表信息。
        该函数会根据传入的用户名、姓名和部门 ID 动态构建 SQL 查询语句，
        从数据库中查询用户列表信息，并将结果整理成字典列表返回。

        :param request: 请求对象
        :param username: 用户名，用于筛选用户列表
        :param fullname: 姓名，用于筛选用户列表
        :param department_id: 部门 ID，用于筛选用户列表
        :return: 包含查询结果的响应字典
        """
        title = "获取用户列表数据"
        res = ""
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        try:
            # 获取用户列表信息
            sql = ' select au.id,au.username,au.fullname,sd.department_name,sd.department_id,au.mobile,au.sex,au.status,au.create_time,au.email,au.age,au.educational_background,au.ethnicity,au.id_card from "auth_user" au left join "WJYY_GZ_SYS_DEPARTMENT" sd on au.department_id = sd.department_id  where au.department_id=sd.department_id and au.is_superuser=false '
            # 如果用户名不为空，添加用户名的模糊查询条件到 SQL 语句中
            if username is not None and str(username).strip() != "":
                sql += " and au.username like '%{}%'".format(username)
            # 如果姓名不为空，添加姓名的模糊查询条件到 SQL 语句中
            if fullname is not None and str(fullname).strip() != "":
                sql += " and au.fullname like '%{}%'".format(fullname)
            # 如果部门 ID 不为空，添加部门 ID 的精确查询条件到 SQL 语句中
            if department_id is not None and str(department_id).strip() != "":
                sql += " and au.department_id = {}".format(department_id)
            # 按照用户创建时间降序排序
            sql += " order by au.create_time desc"
            # 记录 SQL 查询语句到日志中
            logger.info(f"获取用户列表数据 sql = :{sql}")
            # 获取数据库连接的游标
            cursor = self.connection.cursor()
            # 执行 SQL 查询语句
            cursor.execute(sql)
            # 获取查询结果的所有行
            records = cursor.fetchall()
            data_list = []
            for record in records:
                obj = {}
                # 将查询结果的各字段值存储到字典中
                obj['user_id'] = int(record[0])
                obj['user_name'] = str(record[1])
                obj['full_name'] = str(record[2])
                # obj['department_name'] = SysmanHelper.getFullDepartName(int(record[4]), self.connection)
                obj['department_name'] = str(record[3])
                # 调用 SysmanHelper 的 getRoleByUser 方法获取用户的角色信息
                obj['role_id_list'], obj['role_name_list'] = SysmanHelper.getRoleByUser(int(record[0]), self.connection)
                obj['mobile'] = str(record[5])
                obj['sex'] = str(record[6])
                # 根据用户状态值将其转换为文字描述
                obj['status'] = "正常" if int(record[7]) == 1 else "停用"
                if int(record[7]) == 2:
                    obj['status'] = "待审核"
                obj['create_time'] = str(record[8])
                obj['email'] = str(record[9])
                obj['age'] = str(record[10])
                obj['educational_background'] = str(record[11])
                obj['ethnicity'] = str(record[12])
                obj['id_card'] = str(record[13])
                # 将整理好的用户信息字典添加到数据列表中
                data_list.append(obj)
            res = {
                'success': True,
                'total': len(data_list),
                'info': data_list
            }
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          title)
        except Exception as exp:
            # 记录异常日志信息
            res = LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, request.path,
                                                             request.auth.user, request,
                                                             title, None, exp)
        finally:
            return res

    # 退出
    def set_status(self, request):
        """
        设置用户状态的功能。
        该函数会从请求数据中获取用户 ID 和用户状态，然后更新数据库中对应用户的状态。

        :param request: 请求对象，包含用户 ID 和用户状态的请求数据
        :return: 包含设置结果的响应字典
        """
        # 从请求数据中获取用户 ID
        user_id = request.data["user_id"]
        # 从请求数据中获取用户状态
        user_status = request.data["user_status"]
        title = "设置用户状态"
        res = ""
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        try:
            # 根据用户 ID 过滤出对应的用户记录，并更新其状态
            AuthUser.objects.filter(id=user_id).update(status=user_status)
            res = {
                'success': True,
                'info': "{}成功".format(title)
            }
            # 记录日志结束信息
            LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                          title)
        except Exception as exp:
            # 记录异常日志信息
            res = LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, request.path,
                                                             request.auth.user, request,
                                                             title, None, exp)
        finally:
            return res


    def logout(self, request, Token, user_id, auth):
        """
        用户退出登录功能。
        该函数会删除用户的登录 Token 信息，并让用户退出登录，最后记录日志并返回退出成功的响应。

        :param request: 请求对象
        :param Token: Token 模型类，用于操作数据库中的 Token 信息
        :param user_id: 用户的 ID
        :param auth: 认证对象，用于处理用户的登录和退出操作
        :return: 包含退出结果的响应字典
        """
        function_title = "用户退出"
        # 获取当前请求的用户
        user = request.user
        # 记录日志开始信息
        start = LoggerHelper.set_start_log_info(logger)
        # 删除登录的 token 信息，根据用户 ID 过滤出对应的 Token 并删除
        old_token = Token.objects.filter(user_id=user_id)
        old_token.delete()
        # # 删除登录的保险类型记录信息
        # old_insurance = SysUserLogin.objects.filter(user_id=user_id)
        # old_insurance.delete()
        # 调用认证对象的 logout 方法让用户退出登录
        auth.logout(request)
        res = {
            'code': 0,
            'message': '用户退出成功！',
            "userid": user_id,
        }
        # 记录日志结束信息
        LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, user, request, function_title)
        return res

        # 生成验证码


def get_verifaction_code(self, request):
    """
    生成验证码的功能。
    该函数会生成一个包含随机字符的验证码图片，将验证码存储到 session 中，
    并将图片以 HTTP 响应的形式返回给客户端。

    :param request: 请求对象
    :return: 包含验证码图片的 HTTP 响应
    """
    function_title = "生成验证码"
    # 记录日志开始信息
    start = LoggerHelper.set_start_log_info(logger)
    # 背景颜色，随机生成 RGB 值
    bgcolor = (random.randrange(10, 160), random.randrange(50, 160), 255)
    # 宽高
    width = 140
    height = 60
    # 创建画板，使用指定的宽度、高度和背景颜色创建一个 RGB 模式的图片对象
    img = Image.new(mode='RGB', size=(width, height), color=bgcolor)
    # 创建画笔，用于在图片上绘制文字等内容
    draw = ImageDraw.Draw(img, mode='RGB')
    # 定义字符，用于生成验证码的字符集
    text = 'ABCDEFGH12345678'
    # 字体对象，字体，字号，使用指定路径的字体文件和字号创建字体对象
    font1 = ImageFont.truetype(MyAppConfig.verification_font_path, 30)
    # temp 用来存储随机生成的验证码
    temp = ''
    for i in range(6):
        # 每循环一次，从 text 中随机选择一个字符
        temp1 = text[random.randrange(0, len(text))]
        # 把生成的随机码存起来
        temp += temp1
        # 每一次生成新的颜色，随机生成 RGB 值
        color1 = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
        # 把文字写到 img 中，指定文字的位置、内容、颜色和字体
        draw.text((i * 24, i * 6), temp1, color1, font1)
    # 保存到内存流，创建一个内存中的字节流对象
    buf = io.BytesIO()
    # 将图片以 PNG 格式保存到字节流中
    img.save(buf, 'png')
    # 将验证码保存并传递，将生成的验证码存储到 session 中
    request.session['code'] = temp
    # 记录日志结束信息
    LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, None, request, function_title)
    # 将得到的对象返回，返回包含验证码图片的 HTTP 响应，指定响应内容类型为图片 PNG
    return HttpResponse(buf.getvalue(), 'image/png')

    # 获取用户列表-sql


def sql_search(self, request, username, fullname, department_id):
    """
    根据条件查询用户列表信息。
    该函数会根据传入的用户名、姓名和部门 ID 动态构建 SQL 查询语句，
    从数据库中查询用户列表信息，并将结果整理成字典列表返回。

    :param request: 请求对象
    :param username: 用户名，用于筛选用户列表
    :param fullname: 姓名，用于筛选用户列表
    :param department_id: 部门 ID，用于筛选用户列表
    :return: 包含查询结果的响应字典
    """
    title = "获取用户列表数据"
    res = ""
    # 记录日志开始信息
    start = LoggerHelper.set_start_log_info(logger)
    try:
        # 获取用户列表信息
        sql = ' select au.id,au.username,au.fullname,sd.department_name,sd.department_id,au.mobile,au.sex,au.status,au.create_time,au.email,au.age,au.educational_background,au.ethnicity,au.id_card from "auth_user" au left join "WJYY_GZ_SYS_DEPARTMENT" sd on au.department_id = sd.department_id  where au.department_id=sd.department_id and au.is_superuser=false '
        # 如果用户名不为空，添加用户名的模糊查询条件到 SQL 语句中
        if username is not None and str(username).strip() != "":
            sql += " and au.username like '%{}%'".format(username)
        # 如果姓名不为空，添加姓名的模糊查询条件到 SQL 语句中
        if fullname is not None and str(fullname).strip() != "":
            sql += " and au.fullname like '%{}%'".format(fullname)
        # 如果部门 ID 不为空，添加部门 ID 的精确查询条件到 SQL 语句中
        if department_id is not None and str(department_id).strip() != "":
            sql += " and au.department_id = {}".format(department_id)
        # 按照用户创建时间降序排序
        sql += " order by au.create_time desc"
        # 记录 SQL 查询语句到日志中
        logger.info(f"获取用户列表数据 sql = :{sql}")
        # 获取数据库连接的游标
        cursor = self.connection.cursor()
        # 执行 SQL 查询语句
        cursor.execute(sql)
        # 获取查询结果的所有行
        records = cursor.fetchall()
        data_list = []
        for record in records:
            obj = {}
            # 将查询结果的各字段值存储到字典中
            obj['user_id'] = int(record[0])
            obj['user_name'] = str(record[1])
            obj['full_name'] = str(record[2])
            # obj['department_name'] = SysmanHelper.getFullDepartName(int(record[4]), self.connection)
            obj['department_name'] = str(record[3])
            # 调用 SysmanHelper 的 getRoleByUser 方法获取用户的角色信息
            obj['role_id_list'], obj['role_name_list'] = SysmanHelper.getRoleByUser(int(record[0]), self.connection)
            obj['mobile'] = str(record[5])
            obj['sex'] = str(record[6])
            # 根据用户状态值将其转换为文字描述
            obj['status'] = "正常" if int(record[7]) == 1 else "停用"
            obj['create_time'] = str(record[8])
            obj['email'] = str(record[9])
            obj['age'] = str(record[10])
            obj['educational_background'] = str(record[11])
            obj['ethnicity'] = str(record[12])
            obj['id_card'] = str(record[13])
            # 将整理好的用户信息字典添加到数据列表中
            data_list.append(obj)
        res = {
            'success': True,
            'total': len(data_list),
            'info': data_list
        }
        # 记录日志结束信息
        LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                      title)
    except Exception as exp:
        # 记录异常日志信息
        res = LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, request.path,
                                                         request.auth.user, request,
                                                         title, None, exp)
    finally:
        return res

    # 设置用户状态


def set_status(self, request):
    """
    设置用户状态的功能。
    该函数会从请求数据中获取用户 ID 和用户状态，然后更新数据库中对应用户的状态。

    :param request: 请求对象，包含用户 ID 和用户状态的请求数据
    :return: 包含设置结果的响应字典
    """
    # 从请求数据中获取用户 ID
    user_id = request.data["user_id"]
    # 从请求数据中获取用户状态
    user_status = request.data["user_status"]
    title = "设置用户状态"
    res = ""
    # 记录日志开始信息
    start = LoggerHelper.set_start_log_info(logger)
    try:
        # 根据用户 ID 过滤出对应的用户记录，并更新其状态
        AuthUser.objects.filter(id=user_id).update(status=user_status)
        res = {
            'success': True,
            'info': "{}成功".format(title)
        }
        # 记录日志结束信息
        LoggerHelper.set_end_log_info(SysLog, logger, start, request.path, request.auth.user, request,
                                      title)
    except Exception as exp:
        # 记录异常日志信息
        res = LoggerHelper.set_end_log_info_in_exception(SysLog, logger, start, request.path,
                                                         request.auth.user, request,
                                                         title, None, exp)
    finally:
        return res
