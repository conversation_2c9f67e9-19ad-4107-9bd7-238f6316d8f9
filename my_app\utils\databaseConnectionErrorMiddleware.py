from django.db import OperationalError
from django.http import JsonResponse

class DatabaseConnectionErrorMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        try:
            response = self.get_response(request)
            return response
        except OperationalError:
            return JsonResponse({'message': '数据库连接断开，请稍后再试', 'success': False}, status=500)