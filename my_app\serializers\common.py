#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time : 2024-12-26 当前系统时间
# <AUTHOR> zhous
# @Email : <EMAIL>
# @File : common.py
# @Descr : 通用序列化器
# @Software: VSCode

from rest_framework import serializers

class CommonResponseSerializer(serializers.Serializer):
    """通用API响应序列化器 - 支持所有HTTP状态码"""
    success = serializers.BooleanField(help_text="是否成功")
    code = serializers.IntegerField(help_text="响应代码")
    message = serializers.CharField(help_text="响应消息")
    data = serializers.JSONField(help_text="响应数据", required=False, allow_null=True)
    errors = serializers.JSONField(help_text="错误详情", required=False, allow_null=True)

    @staticmethod
    def success_response(data=None, message="操作成功", code=0):
        """创建成功响应"""
        return {
            'success': True,
            'code': code,
            'message': message,
            'data': data
        }

    @staticmethod
    def error_response(message="操作失败", errors=None, code=1):
        """创建错误响应"""
        response = {
            'success': False,
            'code': code,
            'message': message
        }
        if errors:
            response['errors'] = errors
        return response
