#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
# @Time    :  2022/12/15 20:08
# <AUTHOR> chenxw
# @Email   : <EMAIL>
# @File    : sysManager.py
# @Descr   : 系统管理
# @Software: PyCharm
import datetime
import json
import logging
import time
from rest_framework.response import Response
from django.http import QueryDict
from loguru import logger
from vgis_log.logTools import LoggerHelper
from vgis_utils.vgis_http.httpTools import HttpHelper
from vgis_utils.vgis_list.listTools import ListHelper

from my_app.models import SysDepartment, TtServiceData
from my_app.models import SysLog
from my_app.serializers import TtServiceDataSerializer
from my_app.utils.commonUtility import CommonHelper
from my_app.utils.supermapUtility import SupermapHelper
from my_app.utils.sysmanUtility import SysmanHelper
from my_app.views.response.baseRespone import Result
from my_project import settings
from my_project.customPageNumberPagination import CustomPageNumberPagination
from my_project.settings import SUPERMAP_ISERVER_URL

logger = logging.getLogger('django')

# 数据字典的配置信息（字典类别名称，字典表名称，字典字段名称）
dict_catelog_list = [
    {'id': 1818845413654528, 'dict_catelog_name': '标注类型', "dict_table_name": "tt_annotation_type",
     "dict_field_name": "annotation_cname"},
    {'id': 1818845413654529, 'dict_catelog_name': '敌我信息', "dict_table_name": "tt_friend_foe_info",
     "dict_field_name": "info_cname"},
    {'id': 1818845413654530, 'dict_catelog_name': '情报来源', "dict_table_name": "tt_intelligence_source",
     "dict_field_name": "source_cname"},

]
# 特殊字典的ID列表，这个数据表里包含了多个字典类别
yy_shijian_list = [{'dict_catelog_id': 1818845413654531, 'statu_type': 1},
                   {'dict_catelog_id': 1818845413654532, 'statu_type': 2},
                   {'dict_catelog_id': 1818845413654533, 'statu_type': 3},
                   {'dict_catelog_id': 1818845413654534, 'statu_type': 4},
                   {'dict_catelog_id': 1818845413654535, 'statu_type': 5},
                   {'dict_catelog_id': 1818845413654536, 'statu_type': 6}]


class SysOperator:
    def __init__(self, connection):
        self.connection = connection

    def sql_search_menue(self, request):

        params = request.data
        sql = ''' 
                              SELECT  * FROM	"WJYY_GZ_SYS_MENU"     where 1=1  '''
        conditions = []
        timearr = ["querystarttime", "queryendtime", "cjsj_end", "create_time_start", "create_time_end","page_size","page","page_close"]
        bumberarr = ["id", "jd", "wd", "menu_id","parent_id"]
        for key, value in params.items():
            if key == "menu_ids" and isinstance(value, list) and len(value) > 0:
                # 处理 menu_ids 数组
                menu_ids_str = ', '.join(map(str, value))
                conditions.append(f' "WJYY_GZ_SYS_MENU".menu_id IN ({menu_ids_str}) ')
            elif key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f' "WJYY_GZ_SYS_MENU".{key} LIKE \'%{value}%\' ' )
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f' "WJYY_GZ_SYS_MENU".{key} = {value} ')
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)

        if "querystarttime" in params and params["querystarttime"] != "":
            sql += " AND create_date >= '{}' and create_date <= '{}'".format(params["querystarttime"], params["queryendtime"])


        # 排序和分页前的原始SQL用于计算总数
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")

        # 执行总数查询
        cursor = self.connection.cursor()
        cursor.execute(total_sql)
        total_result = cursor.fetchone()
        total = total_result[0] if total_result else 0

        page_close = bool(params.get("page_close", False))
        if not page_close:

            # 添加排序和分页
            sql += " order by id desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            sql += " order by menu_id asc"
        # 执行主查询
        cursor.execute(sql)
        records = cursor.fetchall()
        titile = [title[0] for title in cursor.description]
        data_list = [dict(list(zip(titile, item))) for item in records]

        # 返回数据和总数
        return {"data": data_list, "total": total}

    # 该方法用于设置服务的状态
    def set_service_status(self, request):
        # 从请求数据中提取服务的英文名称
        service_ename = request.data["service_ename"]
        # 从请求数据中提取服务的状态
        service_status = request.data["service_status"]
        # 定义操作的标题，用于日志记录和返回信息
        title = "设置服务状态"
        # 初始化响应结果变量
        res = ""
        # 记录操作开始的时间（高精度时间）
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 将服务英文名称按斜杠分割成两部分
            service_name_splits = service_ename.split("/")
            # 调用 SupermapHelper 类的静态方法，设置服务实例的状态
            SupermapHelper.set_service_instance_status(service_name_splits[0], service_name_splits[1], service_status)
            # 在数据库中过滤出指定服务英文名称的记录，并更新其服务状态
            TtServiceData.object.filter(service_ename=service_ename).update(service_status=service_status)
            # 构建成功响应结果
            res = {
                'success': True,
                'info': "{}成功".format(title)
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的时间（高精度时间）
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的时间（高精度时间）
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 查询导入服务的方法
    def query_service_data_list(self, request):
        # 获取请求数据
        data = request.data
        # 从请求数据中获取页码
        page = data.get('page')
        # 从请求数据中获取每页显示的数量
        size = data.get('size')

        # 检查页码和每页显示数量是否都提供了
        if page is None or size is None:
            # 若未提供，定义错误信息
            msg = "page 和size 都是必传参数"
            # 将错误信息转换为 JSON 字符串
            json_data = json.dumps(msg)
            # 返回失败结果
            return Result.fail(msg, json_data)
        # 检查每页显示数量是否超过最大限制
        if size > settings.PAGE_MAX_SIZE:
            # 若超过，定义错误信息
            msg = "size 不可以大于" + str(settings.PAGE_MAX_SIZE)
            # 将错误信息转换为 JSON 字符串
            json_data = json.dumps(msg)
            # 返回失败结果
            return Result.fail(msg, json_data)
        # 检查页码或每页显示数量是否为 0
        if page == 0 or size == 0:
            # 若为 0，定义错误信息
            msg = "page 或者size 不可以大于0 "
            # 将错误信息转换为 JSON 字符串
            json_data = json.dumps(msg)
            # 返回失败结果
            return Result.fail(msg, json_data)

        # 调用 SysmanHelper 类的方法获取服务数据查询结果
        results = SysmanHelper.get_service_data_query_result(data, request)

        if results:
            # 若有查询结果，计算结果总数
            page_count = results.count()
            # 创建自定义分页器实例
            customPageNumberPagination = CustomPageNumberPagination()
            # 设置分页器的当前页码
            customPageNumberPagination.page = page
            # 设置分页器的每页显示数量
            customPageNumberPagination.page_size = size
            # 创建一个可变的 QueryDict 对象
            new_query_params = QueryDict(mutable=True)
            # 更新 QueryDict 对象的页码参数
            new_query_params.update({'page': page})
            # 将新的 QueryDict 对象赋值给请求的 GET 属性
            request._request.GET = new_query_params
            # 使用分页器对查询结果进行分页
            r_page = customPageNumberPagination.paginate_queryset(results, request, self)
            # 对当前页的数据进行序列化
            articles_serializer = TtServiceDataSerializer(r_page, many=True)

            # 遍历序列化后的数据，对数据进行处理
            for d in articles_serializer.data:
                # 以下注释部分是原本要移除的字段，当前代码中注释掉未执行移除操作
                # d.pop('lng')
                # d.pop('lat')
                # d.pop('elv')
                # d.pop('heading')
                # d.pop('pitch')
                # d.pop('roll')
                # d.pop('remark')
                # d.pop('create_user_id')
                # d.pop('modify_user_id')
                # 为服务 URL 添加前缀
                d['service_url'] = SUPERMAP_ISERVER_URL + d['service_url']
                # 处理创建时间，将 T 替换为空格
                d['create_time'] = d['create_time'].replace("T", " ") if d['create_time'] is not None else None
                # 处理修改时间，将 T 替换为空格
                d['modify_time'] = d['modify_time'].replace("T", " ") if d['modify_time'] is not None else None

            # 调用 Result 类的方法构建分页列表响应结果
            res = Result.page_list(articles_serializer.data, page_count)
            # 以下注释部分是原本要添加 URL 头的操作，当前代码中注释掉未执行
            # res["json_url_head"] = CommonHelper.get_url_head()
            # 返回响应结果
            return Response(res)

        else:
            # 若没有查询结果，返回空的分页列表响应结果
            return Response(Result.page_list(0))

    # 获取数据服务详情（数据源、数据集）的方法
    def get_data_service_details(self, request, id):
        # 定义操作的标题，用于日志记录和返回信息
        title = "获取数据服务详情"
        # 初始化响应结果变量
        res = ""
        # 记录操作开始的时间（高精度时间）
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 从数据库中根据 ID 获取服务的英文名称
            service_ename = TtServiceData.objects.get(id=id).service_ename
            # 将服务英文名称按斜杠分割，获取组件名称
            componentName = service_ename.split("/")[0]
            # 将服务英文名称按斜杠分割，获取接口名称
            interfaceName = service_ename.split("/")[1]

            # 调用 SupermapHelper 类的方法获取数据服务的数据源列表
            datasourceNames = SupermapHelper.get_datasource_list_of_data_service(componentName, interfaceName)
            # 初始化数据源信息列表
            datainfo_list = []
            # 遍历数据源名称列表
            for index2 in range(len(datasourceNames)):
                # 获取当前数据源名称
                datasourceName = datasourceNames[index2]
                # 初始化数据源对象
                datasource_obj = {}
                # 设置数据源对象的数据源名称
                datasource_obj["datasourceName"] = datasourceName
                # 初始化数据集列表
                dataset_list = []
                # 调用 SupermapHelper 类的方法获取当前数据源下的数据集列表
                datasetNames = SupermapHelper.get_dataset_list_of_data_service_by_datasource(componentName,
                                                                                             interfaceName,
                                                                                             datasourceName)
                # 遍历数据集名称列表
                for index in range(len(datasetNames)):
                    # 获取当前数据集名称
                    datasetName = datasetNames[index]
                    # 初始化数据集对象
                    dataset_obj = {}
                    # 设置数据集对象的数据集名称
                    dataset_obj["datasetName"] = datasetName
                    # 对于第一个数据源的第一个数据集，获取其记录值信息
                    if index2 == 0 and index == 0:
                        # 调用 SupermapHelper 类的方法获取记录值信息
                        records = SupermapHelper.get_record_value_of_data_service_by_datasource_dataset(componentName,
                                                                                                        interfaceName,
                                                                                                        datasourceName,
                                                                                                        datasetName)
                        # 初始化记录列表对象
                        record_list = {}
                        # 设置记录列表对象的字段列表
                        record_list["fieldlist"] = records[0]["fieldNames"]
                        # 初始化值列表
                        value_list = []
                        # 遍历记录信息
                        for record in records:
                            # 初始化值对象
                            value_obj = {}
                            # 设置值对象的字段值
                            value_obj["fieldValues"] = record["fieldValues"]
                            # 设置值对象的几何类型
                            value_obj["type"] = record["geometry"]["type"]
                            # 设置值对象的几何点信息
                            value_obj["geometry"] = record["geometry"]["points"]
                            # 将值对象添加到值列表中
                            value_list.append(value_obj)
                        # 设置记录列表对象的值列表
                        record_list["valuelist"] = value_list
                        # 设置数据集对象的记录列表
                        dataset_obj["recordlist"] = record_list
                    # 将数据集对象添加到数据集列表中
                    dataset_list.append(dataset_obj)
                # 设置数据源对象的数据集列表
                datasource_obj["datasetlist"] = dataset_list
            # 将数据源对象添加到数据源信息列表中
            datainfo_list.append(datasource_obj)

            # 构建成功响应结果
            res = {
                'success': True,
                # 'total': len(data_list),
                'info': datainfo_list
            }

            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的时间（高精度时间）
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的时间（高精度时间）
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 获取数据服务详情(指定数据源和数据集后的要素信息）
    # 获取指定数据源和数据集下的数据服务要素记录
    def get_data_service_records_by_datasource_dataset(self, request):
        # 操作标题，用于日志记录和结果反馈
        title = "获取数据服务要素记录"
        # 从请求数据中获取服务的 ID
        id = request.data["id"]
        # 从请求数据中获取数据源名称
        datasourceName = request.data["datasourceName"]
        # 从请求数据中获取数据集名称
        datasetName = request.data["datasetName"]
        # 初始化响应结果
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 初始化数据集对象
            dataset_obj = {}
            # 设置数据集对象的名称
            dataset_obj["datasetName"] = datasetName
            # 根据 ID 从数据库中获取服务的英文名称
            service_ename = TtServiceData.objects.get(id=id).service_ename
            # 从服务英文名称中提取组件名称
            componentName = service_ename.split("/")[0]
            # 从服务英文名称中提取接口名称
            interfaceName = service_ename.split("/")[1]
            # 调用 SupermapHelper 类的方法，获取指定数据源和数据集下的数据服务记录值
            records = SupermapHelper.get_record_value_of_data_service_by_datasource_dataset(componentName,
                                                                                            interfaceName,
                                                                                            datasourceName,
                                                                                            datasetName)
            # 初始化记录列表对象
            record_list = {}
            # 设置记录列表对象的字段列表
            record_list["fieldlist"] = records[0]["fieldNames"]
            # 初始化值列表
            value_list = []
            # 遍历每条记录
            for record in records:
                # 初始化值对象
                value_obj = {}
                # 设置值对象的字段值
                value_obj["fieldValues"] = record["fieldValues"]
                # 设置值对象的几何类型
                value_obj["type"] = record["geometry"]["type"]
                # 设置值对象的几何点信息
                value_obj["geometry"] = record["geometry"]["points"]
                # 将值对象添加到值列表中
                value_list.append(value_obj)
            # 设置记录列表对象的值列表
            record_list["valuelist"] = value_list
            # 将记录列表对象添加到数据集对象中
            dataset_obj["recordlist"] = record_list

            # 构建成功响应结果
            res = {
                'success': True,
                # 'total': len(data_list),
                'info': dataset_obj
            }

            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 获取可导入的服务列表
    def get_importable_service(self, request):
        # 从请求的查询参数中获取服务类型，默认为空字符串
        service_type = request.query_params.get('service_type', '')
        # 操作标题，用于日志记录和结果反馈
        title = "获取可导入服务列表"
        # 初始化响应结果
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 调用 SupermapHelper 类的方法，获取服务列表
            service_data = SupermapHelper.get_service_list()
            # 调用 SupermapHelper 类的方法，获取工作空间服务列表
            workspace_service_data = SupermapHelper.get_workspace_service_list()
            # 初始化数据列表
            data_list = []
            # 遍历服务列表中的每条记录
            for record in service_data:
                # 将服务接口类型按逗号分割成列表
                service_interface_types = record["interfaceNames"].split(",")
                # 遍历每个服务接口类型
                for service_interface_type in service_interface_types:
                    # 初始化服务对象
                    obj = {}
                    # 设置服务对象的英文名称
                    obj['service_ename'] = record["name"]
                    # 设置服务对象的中文名称
                    obj['service_cname'] = record["alias"]
                    # obj['url'] = record["url"]
                    # 设置服务对象的别名
                    obj["service_aliasname"] = record["alias"]
                    # obj["enable"] = record["enable"]
                    # obj["service_providerNames"] = record["providerNames"]
                    # 设置服务对象的接口类型
                    obj["service_interface_type"] = service_interface_type
                    # 重新设置服务对象的英文名称，包含接口类型
                    obj["service_ename"] = record["name"] + "/" + service_interface_type
                    # 调用 CommonHelper 类的方法，判断服务是否启用
                    service_enable = CommonHelper.get_enabled_by_service_name(workspace_service_data,
                                                                              obj['service_ename'])
                    # 根据服务是否启用设置服务状态
                    obj["service_status"] = 1 if service_enable else 0
                    # 设置服务对象的 URL
                    obj["service_url"] = SUPERMAP_ISERVER_URL + "iserver/services/" + obj["service_ename"]

                    # 如果请求的服务类型是地图服务，并且当前服务类型是地图服务实现
                    if service_type == "map" and record["type"] == "com.supermap.services.components.impl.MapImpl":
                        # 设置服务对象的组件类型为地图服务
                        obj["service_component_type"] = "地图服务"
                        # 如果接口类型是 rest 且服务名称中不包含 mvt
                        if service_interface_type == "rest" and "mvt" not in record["name"]:
                            # 调用 SupermapHelper 类的方法，获取服务实例的地图信息
                            maps_info = SupermapHelper.get_maps_of_service_instance(record["name"],
                                                                                    service_interface_type)
                            # 初始化地图名称列表
                            maps_list = []
                            # 遍历地图信息，提取地图名称
                            for map_info in maps_info:
                                maps_list.append(map_info["name"])
                            # 将地图名称列表用逗号连接成字符串
                            obj["map_names"] = ",".join(maps_list)
                        else:
                            # 否则，地图名称为空
                            obj["map_names"] = None
                        # 将服务对象添加到数据列表中
                        data_list.append(obj)
                    # 如果请求的服务类型是数据服务，并且当前服务类型是数据服务实现
                    if service_type == "data" and record["type"] == "com.supermap.services.components.impl.DataImpl":
                        # 设置服务对象的组件类型为数据服务
                        obj["service_component_type"] = "数据服务"
                        # 地图名称为空
                        obj["map_names"] = None
                        # 将服务对象添加到数据列表中
                        data_list.append(obj)
                    # 如果请求的服务类型是三维服务，并且当前服务类型是三维服务实现
                    if service_type == "3d" and record["type"] == "com.supermap.services.components.impl.RealspaceImpl":
                        # 设置服务对象的组件类型为三维服务
                        obj["service_component_type"] = "三维服务"
                        # 地图名称为空
                        obj["map_names"] = None
                        # 将服务对象添加到数据列表中
                        data_list.append(obj)
            # 构建成功响应结果
            res = {
                'success': True,
                'total': len(data_list),
                'info': data_list
            }

            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 通过 SQL 查询获取部门列表
    def sql_search_department(self, request, department_name, department_status):
        # 操作标题，用于日志记录和结果反馈
        title = "获取部门列表数据"
        # 初始化响应结果
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 初始化 SQL 查询语句，筛选出删除标志为 0 的记录
            sql = 'select department_id,department_name,parent_id,state,order_num,create_time,master,tel,email,del_flag from "WJYY_GZ_SYS_DEPARTMENT"  where 1=1 and del_flag =0'
            # 如果部门名称不为空，添加模糊查询条件
            if department_name is not None and str(department_name).strip() != "":
                sql += " and department_name like '%{}%'".format(department_name)
            # 如果部门状态不为空，添加状态查询条件
            if department_status is not None and str(department_status).strip() != "":
                sql += " and state ='{}'".format(department_status)
            # 按创建时间降序排序
            sql += " order by create_time desc"
            # 获取数据库连接的游标
            cursor = self.connection.cursor()
            # 记录 SQL 查询语句到日志
            logger.debug("执行SQL查询: %s", sql)
            # 执行 SQL 查询语句
            cursor.execute(sql)
            # 获取查询结果
            records = cursor.fetchall()
            # 初始化数据列表
            data_list = []
            # 遍历查询结果
            for record in records:
                # 初始化部门对象
                obj = {}
                # 设置部门对象的 ID
                obj['department_id'] = int(record[0])
                # 设置部门对象的名称
                obj['department_name'] = str(record[1])
                # 设置部门对象的父部门 ID
                obj['parent_id'] = int(record[2])
                # 调用 SysmanHelper 类的方法，获取父部门信息
                department_id, department_name, parent_id = SysmanHelper.getDepartInfo(obj['parent_id'],
                                                                                       self.connection)
                # 设置部门对象的父部门名称
                obj['parent_name'] = department_name
                # 根据部门状态设置部门对象的状态描述
                obj['state'] = "正常" if int(record[3]) == 1 else "停用"
                # 设置部门对象的排序号
                obj['order_num'] = int(record[4])
                # 设置部门对象的创建时间
                obj['create_time'] = str(record[5])
                # 设置部门对象的负责人
                obj['master'] = str(record[6])
                # 设置部门对象的联系电话
                obj['tel'] = str(record[7])
                # 设置部门对象的邮箱
                obj['email'] = str(record[8])
                # 设置部门对象的删除标志
                obj['del_flag'] = str(record[9])
                # obj['company_name'] = str(record[10])
                # obj['department_code'] = str(record[10])
                # 将部门对象添加到数据列表中
                data_list.append(obj)

            # 构建成功响应结果
            res = {
                'success': True,
                'total': len(data_list),
                'info': data_list
            }

            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            if request.auth == None:
                request.auth = ""
                # 记录日志相关信息
                logger.info("操作日志: 用户=%s, 标题=%s, 路径=%s, 耗时=%s秒, IP=%s",
                           request.auth, title, request.path, t, HttpHelper.get_ip_request(request))
                # 将操作日志信息插入到数据库中
                LoggerHelper.insert_log_info(SysLog, request.auth, title, request.path,
                                             HttpHelper.get_params_request(request),
                                             t, HttpHelper.get_ip_request(request))
            else:
                # 记录日志相关信息
                logger.info("操作日志: 用户=%s, 标题=%s, 路径=%s, 耗时=%s秒, IP=%s",
                           request.auth.user, title, request.path, t, HttpHelper.get_ip_request(request))
                # 将操作日志信息插入到数据库中
                LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                             HttpHelper.get_params_request(request),
                                             t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，最终都返回响应结果
            return res

    # 设置部门状态
    # 设置部门状态的方法
    def set_department_status(self, request):
        # 从请求数据中获取要设置状态的部门 ID
        department_id = request.data["department_id"]
        # 从请求数据中获取要设置的部门状态
        department_status = request.data["department_status"]
        # 定义操作的标题，用于日志记录和结果信息展示
        title = "设置部门状态"
        # 初始化响应结果变量
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 在数据库中找到指定部门 ID 的记录，并更新其状态
            SysDepartment.objects.filter(department_id=department_id).update(state=department_status)
            # 构建成功响应结果
            res = {
                'success': True,
                'info': "{}成功".format(title)
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 逻辑删除部门及下属部门的方法
    def delete_department(self, request):
        # 从请求数据中获取要删除的部门 ID
        department_id = request.data["department_id"]
        # 定义操作的标题，用于日志记录和结果信息展示
        title = "逻辑删除部门数据"
        # 初始化响应结果变量
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 调用 SysmanHelper 类的方法，获取当前部门的所有下级部门 ID 列表（暂时支持三级部门）
            department_id_list = SysmanHelper.getDepartIdAllLevel(department_id, self.connection)
            # 构建 SQL 更新语句，将指定部门及其下级部门的删除标志设置为 1（表示逻辑删除）
            sql = 'update "WJYY_GZ_SYS_DEPARTMENT"  set del_flag=1 where department_id in ({})'.format(
                ListHelper.get_number_str_by_list(department_id_list))
            # 获取数据库连接的游标
            cursor = self.connection.cursor()
            # 执行 SQL 更新语句
            cursor.execute(sql)
            # 提交数据库事务
            self.connection.commit()
            # 构建成功响应结果
            res = {
                'success': True,
                'info': "删除成功，包括本级及下级部门：{}".format(ListHelper.get_number_str_by_list(department_id_list))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 通过 SQL 查询获取角色列表的方法
    def sql_search_role(self, request ):
        # 定义操作的标题，用于日志记录和结果信息展示
        title = "获取角色列表数据"
        # 初始化响应结果变量
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        try:
            param = request.data
            # 检查是否禁用分页
            role_name = param.get("role_name","")
            page_close = param.get("page_close", False)
            if not page_close:
                # 获取分页参数，默认为第 1 页，每页 10 条记录
                page = int(param.get('page', 1))
                page_size = int(param.get('page_size', 10))
                offset = (page - 1) * page_size

            # 初始化 SQL 查询语句，查询角色信息
            sql = 'select role_id,role_name,remark,create_time from "WJYY_GZ_SYS_ROLE"  where 1=1 '
            # 如果传入了角色名称，添加模糊查询条件到 SQL 语句中
            if role_name is not None and str(role_name).strip() != "":
                sql += " and role_name like '%{}%'".format(role_name)
            # 按创建时间降序排序查询结果
            sql += " order by create_time desc"

            if not page_close:
                # 添加分页条件
                sql += " limit {} offset {}".format(page_size, offset)

            # 获取数据库连接的游标
            cursor = self.connection.cursor()
            # 执行 SQL 查询语句
            cursor.execute(sql)
            # 获取查询结果
            records = cursor.fetchall()

            # 初始化存储角色信息的列表
            data_list = []
            # 遍历查询结果
            for record in records:
                # 初始化角色对象
                obj = {}
                # 设置角色对象的 ID
                obj['role_id'] = int(record[0])
                # 设置角色对象的名称
                obj['role_name'] = str(record[1])
                # 调用 SysmanHelper 类的方法，获取该角色对应的菜单 ID 列表
                obj['menu_id_list'] = SysmanHelper.getMenuByRole(int(record[0]), self.connection)
                # 设置角色对象的备注信息
                obj['remark'] = str(record[2])
                # 设置角色对象的创建时间
                obj['create_time'] = str(record[3])
                # 将角色对象添加到角色信息列表中
                data_list.append(obj)

            # 构建成功响应结果
            res = {
                'success': True,
                'total': len(data_list),
                'info': data_list
            }

            if not page_close:
                res['page'] = page
                res['page_size'] = page_size

            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))

        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 替换字符串中指定前后标记之间内容的方法
    def replace_between(self, s, before, after, replacement):
        # 检查输入字符串是否为 None，或者前后标记是否在字符串中存在
        if s is None or s.count(before) == 0 or s.count(after) == 0:
            # 如果不满足条件，直接返回原字符串
            return s
        # 找到前标记的结束位置
        start = s.index(before) + len(before)
        # 从 start 位置开始找到后标记的位置
        end = s.index(after, start)
        # 拼接字符串，将前后标记之间的内容替换为指定的替换内容
        return s[:start] + replacement + s[end:]

    def sql_search_log(self, request):

        params = request.data
        sql = ''' 
                              SELECT  * FROM	"WJYY_GZ_SYS_LOG"     where 1=1  '''
        conditions = []
        timearr = ["querystarttime", "queryendtime", "cjsj_end", "create_time_start", "create_time_end","page_size","page","page_close"]
        bumberarr = ["id", "jd", "wd", "jfmj","log_type"]
        for key, value in params.items():
            if key not in timearr and key not in bumberarr and str(value).strip() != "":
                conditions.append(f' "WJYY_GZ_SYS_LOG".{key} LIKE \'%{value}%\' ' )
            elif key not in timearr and str(value).strip() != "":
                conditions.append(f' "WJYY_GZ_SYS_LOG".{key} = {value} ')
        if len(conditions) > 0:
            sql += ' and '
            sql += " AND ".join(conditions)

        if "querystarttime" in params and params["querystarttime"] != "":
            sql += " AND create_date >= '{}' and create_date <= '{}'".format(params["querystarttime"], params["queryendtime"])


        # 排序和分页前的原始SQL用于计算总数
        total_sql = sql.replace("SELECT  *", "SELECT COUNT(*) as total_count ")

        # 执行总数查询
        cursor = self.connection.cursor()
        cursor.execute(total_sql)
        total_result = cursor.fetchone()
        total = total_result[0] if total_result else 0

        page_close = bool(params.get("page_close", False))
        if not page_close:

            # 添加排序和分页 id or create_date
            sql += " order by create_date desc LIMIT {} OFFSET {}".format(
                int(params.get("page_size", 10)),
                (int(params.get("page", 1)) - 1) * int(params.get("page_size", 10))
            )
        else:
            #sql += " order by id desc"
            sql += " order by create_date desc"
        # 执行主查询
        cursor.execute(sql)
        records = cursor.fetchall()
        titile = [title[0] for title in cursor.description]
        data_list = [dict(list(zip(titile, item))) for item in records]

        # 返回数据和总数
        return {"data": data_list, "total": total}

    # 获取日志列表-sql
    # 通过 SQL 查询获取日志列表数据
    def sql_search_log2(self, request, username, querystarttime, queryendtime):
        # 操作标题，用于日志记录和结果反馈
        title = "获取日志列表数据"
        # 初始化响应结果
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 初始化 SQL 查询语句，查询日志信息
            sql = 'select id,username,operation,method,params,time,ip,create_date from "WJYY_GZ_SYS_LOG"  where 1=1 '
            # 如果传入了用户名，添加模糊查询条件到 SQL 语句
            if username is not None and str(username).strip() != "":
                sql += " and username like '%{}%'".format(username)
            # 如果传入了查询开始时间，添加时间范围查询条件到 SQL 语句
            if querystarttime is not None and str(querystarttime).strip() != "":
                sql += " and create_date >= '{}'".format(querystarttime)
            # 如果传入了查询结束时间，添加时间范围查询条件到 SQL 语句
            if queryendtime is not None and str(queryendtime).strip() != "":
                sql += " and create_date <= '{}'".format(queryendtime)
            # 按创建日期降序排序查询结果
            sql += " order by create_date desc"
            # 获取数据库连接的游标
            cursor = self.connection.cursor()
            # 执行 SQL 查询语句
            cursor.execute(sql)
            # 获取查询结果
            records = cursor.fetchall()
            # 初始化数据列表
            data_list = []
            # 遍历查询结果
            for record in records:
                # 初始化日志对象
                obj = {}
                # 设置日志对象的 ID
                obj['log_id'] = int(record[0]) if record[0] is not None and str(record[0]).strip() != "" else None
                # 设置日志对象的用户名
                obj['username'] = str(record[1])
                # 设置日志对象的操作描述
                obj['operation'] = str(record[2])
                # 设置日志对象的请求方法
                obj['method'] = str(record[3])
                # 设置日志对象的请求参数
                obj['params'] = str(record[4])
                # 如果请求方法中包含 "login"，对密码进行脱敏处理
                if "login" in obj['method']:
                    obj['params'] = self.replace_between(obj['params'], '&password=', '&verifcation=', '******')
                # 设置日志对象的操作耗时
                obj['time'] = int(record[5]) if record[5] is not None and str(record[5]).strip() != "" else None
                # 设置日志对象的 IP 地址
                obj['ip'] = str(record[6])
                # 设置日志对象的创建日期
                obj['create_date'] = str(record[7])
                # 将日志对象添加到数据列表中
                data_list.append(obj)
            # 构建成功响应结果
            res = {
                'success': True,
                'total': len(data_list),
                'info': data_list
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 获取数据字典类别数据
    def get_dict_catelog_list(self, request, title):
        # 初始化响应结果
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 构建成功响应结果，包含数据字典类别列表的信息
            res = {
                'success': True,
                'total': len(dict_catelog_list),
                'info': [{'id': d['id'], 'catelog_name': d['dict_catelog_name']} for d in dict_catelog_list]
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 根据数据字典类别 ID 获取对应的类别信息
    def get_dict_catelog_info_by_id(self, dict_catelog_id):
        # 从数据字典类别列表中筛选出指定 ID 的信息
        return [d for d in dict_catelog_list if d['id'] == dict_catelog_id]

    # 根据数据字典类别 ID 获取对应的状态类型
    def get_statu_type_by_dict_catelog_id(self, dict_catelog_id):
        # 从事件列表中筛选出指定类别 ID 的信息，并获取其状态类型
        return [d for d in yy_shijian_list if d['dict_catelog_id'] == dict_catelog_id][0]['statu_type']

    # 通过 SQL 查询获取数据字典列表数据
    def sql_search_dict(self, request, dict_catelog_id, title):
        # 初始化响应结果
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 根据数据字典类别 ID 获取对应的类别信息
            dict_catelog_info = self.get_dict_catelog_info_by_id(int(dict_catelog_id))
            # 构建 SQL 查询语句，查询归属于该字典类别下的字典信息
            sql = "select id,{},memo from {}  where 1=1 ".format(dict_catelog_info[0]['dict_field_name'],
                                                                 dict_catelog_info[0]['dict_table_name'])
            # 对特定的数据表（tt_yy_shijian_type）里的多个字典类别信息进行特殊处理
            if int(dict_catelog_id) in [d['dict_catelog_id'] for d in yy_shijian_list]:
                sql += " and statu_type = {}".format(self.get_statu_type_by_dict_catelog_id(int(dict_catelog_id)))
            # 按 ID 升序排序查询结果
            sql += " order by id asc"
            # 获取数据库连接的游标
            cursor = self.connection.cursor()
            # 执行 SQL 查询语句
            cursor.execute(sql)
            # 获取查询结果
            records = cursor.fetchall()
            # 初始化数据列表
            data_list = []
            # 遍历查询结果
            for record in records:
                # 初始化字典对象
                obj = {}
                # 设置字典对象的 ID
                obj['id'] = int(record[0])
                # 设置字典对象的类型值
                obj['type_value'] = str(record[1])
                # 设置字典对象的备注信息，如果备注为空则设为空字符串
                obj['memo_value'] = str(record[2]) if record[2] is not None else ""
                # 将字典对象添加到数据列表中
                data_list.append(obj)
            # 构建成功响应结果
            res = {
                'success': True,
                'total': len(data_list),
                'info': data_list
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 通过编号获取数据字典详情
    # 根据条件获取数据字典的详细信息
    def get_detail_by_condition(self, request, dict_catelog_id, id, title):
        # 初始化响应结果
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 根据数据字典类别 ID 获取对应的类别信息
            dict_catelog_info = self.get_dict_catelog_info_by_id(int(dict_catelog_id))
            # 构建 SQL 查询语句，查询归属于该字典类别下且指定 ID 的字典信息
            sql = "select id,{},memo from {}  where 1=1 and id ={} ".format(dict_catelog_info[0]['dict_field_name'],
                                                                            dict_catelog_info[0]['dict_table_name'], id)
            # 对特定数据表（tt_yy_shijian_type）里的多个字典类别信息进行特殊处理
            if int(dict_catelog_id) in [d['dict_catelog_id'] for d in yy_shijian_list]:
                sql += " and statu_type = {}".format(self.get_statu_type_by_dict_catelog_id(int(dict_catelog_id)))
            # 按 ID 升序排序查询结果
            sql += " order by id asc"
            # 获取数据库连接的游标
            cursor = self.connection.cursor()
            # 执行 SQL 查询语句
            cursor.execute(sql)
            # 获取查询结果
            records = cursor.fetchall()
            # 初始化数据列表
            data_list = []
            # 遍历查询结果
            for record in records:
                # 初始化字典对象
                obj = {}
                # 设置字典对象的数据字典类别 ID
                obj['dict_catelog_id'] = dict_catelog_id
                # 设置字典对象的数据字典类别名称
                obj['dict_catelog_name'] = dict_catelog_info[0]['dict_catelog_name']
                # 设置字典对象的 ID
                obj['id'] = int(record[0])
                # 设置字典对象的类型值
                obj['type_value'] = str(record[1])
                # 设置字典对象的备注信息，如果备注为空则设为空字符串
                obj['memo_value'] = str(record[2]) if record[2] is not None else ""
                # 将字典对象添加到数据列表中
                data_list.append(obj)
            # 构建成功响应结果
            res = {
                'success': True,
                'total': len(data_list),
                'info': data_list
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 获取指定表中的最大 ID 值
    def get_max_id(self, tablename):
        # 构建 SQL 查询语句，查询指定表中的最大 ID
        sql = "select max(id) from {}".format(tablename)
        # 获取数据库连接的游标
        cursor = self.connection.cursor()
        # 执行 SQL 查询语句
        cursor.execute(sql)
        # 获取查询结果的第一行
        record = cursor.fetchone()
        # 如果查询结果不为空，返回最大 ID 的整数值；否则返回 0
        return int(record[0]) if record is not None else 0

    # 添加数据字典信息
    def add_dict(self, request, title):
        # 初始化响应结果
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 从请求数据中获取数据字典类别 ID
            dict_catelog_id = request.data.get("dict_catelog_id")
            # 从请求数据中获取类型值
            type_value = request.data.get("type_value")
            # 从请求数据中获取备注信息
            memo_value = request.data.get("memo_value")
            # 根据数据字典类别 ID 获取对应的类别信息
            dict_catelog_info = self.get_dict_catelog_info_by_id(int(dict_catelog_id))
            # 获取数据库连接的游标
            cursor = self.connection.cursor()
            # 构建 SQL 查询语句，检查是否存在重复的字典信息
            sql = "select count(*) from {} where {} ='{}'".format(dict_catelog_info[0]['dict_table_name'],
                                                                  dict_catelog_info[0]['dict_field_name'],
                                                                  type_value)
            # 对特定数据表（tt_yy_shijian_type）里的多个字典类别信息进行特殊处理
            if int(dict_catelog_id) in [d['dict_catelog_id'] for d in yy_shijian_list]:
                sql = "select count(*) from {} where {} ='{}' and statu_type ={}".format(
                    dict_catelog_info[0]['dict_table_name'],
                    dict_catelog_info[0]['dict_field_name'],
                    type_value, self.get_statu_type_by_dict_catelog_id(int(dict_catelog_id)))
            # 执行 SQL 查询语句
            cursor.execute(sql)
            # 获取查询结果的第一行
            record = cursor.fetchone()
            # 如果查询结果的计数大于 0，说明存在重复信息
            if record[0] > 0:
                # 构建失败响应结果
                res = {
                    'success': False,
                    'info': "添加数据字典失败，该字典类别下已存在该字典信息"
                }
                # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
                logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                # 记录操作结束的高精度时间
                end = time.perf_counter()
                # 计算操作总共花费的时间
                t = end - start
                # 记录操作总共花费的时间到日志
                logger.info("总共用时{}秒".format(t))
                # 将操作失败的日志信息插入到数据库中
                LoggerHelper.insert_log_info(SysLog, request.auth.user, res['info'], request.path,
                                             HttpHelper.get_params_request(request),
                                             t, HttpHelper.get_ip_request(request))
            else:
                # 构建 SQL 插入语句，插入新的字典信息
                sql = "insert into {} (id,{},memo) values ({},'{}','{}') ".format(
                    dict_catelog_info[0]['dict_table_name'],
                    dict_catelog_info[0]['dict_field_name'],
                    self.get_max_id(dict_catelog_info[0]['dict_table_name']) + 1,
                    type_value, memo_value
                )
                # 对特定数据表（tt_yy_shijian_type）进行特殊处理
                if int(dict_catelog_id) in [d['dict_catelog_id'] for d in yy_shijian_list]:
                    sql = "insert into {} (id,{},memo,statu_type) values ({},'{}','{}','{}') ".format(
                        dict_catelog_info[0]['dict_table_name'],
                        dict_catelog_info[0]['dict_field_name'],
                        self.get_max_id(dict_catelog_info[0]['dict_table_name']) + 1,
                        type_value, memo_value, self.get_statu_type_by_dict_catelog_id(int(dict_catelog_id))
                    )
                # 执行 SQL 插入语句
                cursor.execute(sql)
                # 提交数据库事务
                self.connection.commit()
                # 构建成功响应结果
                res = {
                    'success': True,
                    'info': "添加数据字典成功"
                }
                # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
                logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                # 记录操作结束的高精度时间
                end = time.perf_counter()
                # 计算操作总共花费的时间
                t = end - start
                # 记录操作总共花费的时间到日志
                logger.info("总共用时{}秒".format(t))
                # 将操作日志信息插入到数据库中
                LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                             HttpHelper.get_params_request(request),
                                             t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 编辑数据字典信息
    def update_dict(self, request, title):
        # 初始化响应结果
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 从请求数据中获取数据字典类别 ID
            dict_catelog_id = request.data.get("dict_catelog_id")
            # 从请求数据中获取要更新的字典信息的 ID
            id = request.data.get("id")
            # 从请求数据中获取类型值
            type_value = request.data.get("type_value")
            # 从请求数据中获取备注信息
            memo_value = request.data.get("memo_value")
            # 根据数据字典类别 ID 获取对应的类别信息
            dict_catelog_info = self.get_dict_catelog_info_by_id(int(dict_catelog_id))
            # 获取数据库连接的游标
            cursor = self.connection.cursor()
            # 构建 SQL 查询语句，检查是否存在除当前记录外的重复字典信息
            sql = "select count(*) from {} where {} ='{}' and id !={}".format(dict_catelog_info[0]['dict_table_name'],
                                                                              dict_catelog_info[0]['dict_field_name'],
                                                                              type_value, int(id))
            # 对特定数据表（tt_yy_shijian_type）里的多个字典类别信息进行特殊处理
            if int(dict_catelog_id) in [d['dict_catelog_id'] for d in yy_shijian_list]:
                sql = "select count(*) from {} where {} ='{}' and statu_type ={} and id !={}".format(
                    dict_catelog_info[0]['dict_table_name'],
                    dict_catelog_info[0]['dict_field_name'],
                    type_value, self.get_statu_type_by_dict_catelog_id(int(dict_catelog_id)), int(id))
            # 执行 SQL 查询语句
            cursor.execute(sql)
            # 获取查询结果的第一行
            record = cursor.fetchone()
            # 如果查询结果的计数大于 0，说明存在重复信息
            if record[0] > 0:
                # 构建失败响应结果
                res = {
                    'success': False,
                    'info': "更新数据字典失败，该字典类别下已存在该字典信息"
                }
                # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
                logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                # 记录操作结束的高精度时间
                end = time.perf_counter()
                # 计算操作总共花费的时间
                t = end - start
                # 记录操作总共花费的时间到日志
                logger.info("总共用时{}秒".format(t))
                # 将操作失败的日志信息插入到数据库中
                LoggerHelper.insert_log_info(SysLog, request.auth.user, res['info'], request.path,
                                             HttpHelper.get_params_request(request),
                                             t, HttpHelper.get_ip_request(request))
            else:
                # 构建 SQL 更新语句，更新指定 ID 的字典信息
                sql = "update  {} set {}='{}',memo='{}' where id={} ".format(
                    dict_catelog_info[0]['dict_table_name'],
                    dict_catelog_info[0]['dict_field_name'], type_value,
                    memo_value, id
                )
                # 执行 SQL 更新语句
                cursor.execute(sql)
                # 提交数据库事务
                self.connection.commit()
                # 构建成功响应结果
                res = {
                    'success': True,
                    'info': "更新数据字典成功"
                }
                # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
                logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                # 记录操作结束的高精度时间
                end = time.perf_counter()
                # 计算操作总共花费的时间
                t = end - start
                # 记录操作总共花费的时间到日志
                logger.info("总共用时{}秒".format(t))
                # 将操作日志信息插入到数据库中
                LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                             HttpHelper.get_params_request(request),
                                             t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time

    # 删除数据字典
    # 删除数据字典的方法
    def delete_dict(self, request, title):
        # 初始化响应结果
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 从请求数据中获取数据字典类别 ID
            dict_catelog_id = request.data.get("dict_catelog_id")
            # 从请求数据中获取要删除的数据字典的 ID
            id = request.data.get("id")
            # 根据数据字典类别 ID 获取对应的类别信息
            dict_catelog_info = self.get_dict_catelog_info_by_id(int(dict_catelog_id))
            # 获取数据库连接的游标
            cursor = self.connection.cursor()
            # 构建 SQL 删除语句，从相应的数据表中删除指定 ID 的数据字典记录
            sql = "delete from  {} where id = {}".format(
                dict_catelog_info[0]['dict_table_name'],
                id
            )
            # 执行 SQL 删除语句
            cursor.execute(sql)
            # 提交数据库事务
            self.connection.commit()
            # 构建成功响应结果
            res = {
                'success': True,
                'info': "{}成功".format(title)
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 通过 SQL 查询获取消息列表的方法
    def sql_search_message(self, request, username, querystarttime, queryendtime, title):
        # 初始化响应结果
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 如果查询开始时间为空，将其设置为 2024-01-01 00:00:00
        if querystarttime == "":
            querystarttime = "2024-01-01 00:00:00"
        # 如果查询结束时间为空，将其设置为当前时间
        if queryendtime == "":
            queryendtime = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 构建 SQL 查询语句，从三个表中联合查询消息相关信息
            sql = '''
                select
                    tableb.content_msg,tableb.create_time,tableb.message_has_valid,tablea.id,tablec.username,tablec.fullname
                from
                    tt_user_message tablea,
                    tt_message_queue tableb,
                    "auth_user" tablec
                where
                    tablea.message_queue_id = tableb.id 
                    and tablea.user_id=tablec.id
                    and tablec.username like '%{}%'
                    and tableb.create_time>='{}'
                    and tableb.create_time<='{}'
                    order by tablec.username
            '''.format(username, querystarttime, queryendtime)
            # 获取数据库连接的游标
            cursor = self.connection.cursor()
            # 执行 SQL 查询语句
            cursor.execute(sql)
            # 获取查询结果
            records = cursor.fetchall()
            # 初始化数据列表
            data_list = []
            # 遍历查询结果
            for record in records:
                # 初始化消息对象
                obj = {}
                # 设置消息对象的 ID
                obj['id'] = int(record[3])
                # 设置消息对象的消息内容
                obj['message'] = str(record[0])
                # 设置消息对象的创建时间
                obj['time'] = str(record[1])
                # 根据消息有效性字段的值，设置消息对象的有效性状态
                obj['has_valid'] = "有效" if record[2] is not None and int(record[2]) == 1 else "无效"
                # 设置消息对象的用户名
                obj['usernam'] = str(record[4])
                # 设置消息对象的用户全名，如果为空则设为空字符串
                obj['fullname'] = str(record[5]) if record[5] is not None else ""
                # 将消息对象添加到数据列表中
                data_list.append(obj)
            # 构建成功响应结果
            res = {
                'success': True,
                'total': len(data_list),
                'info': data_list
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res

    # 删除用户消息的方法
    def delete_message(self, request, title):
        # 初始化响应结果
        res = ""
        # 记录操作开始的高精度时间
        start = time.perf_counter()
        # 记录操作开始的时间到日志，格式为年-月-日 时:分:秒
        logger.info("开始时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        try:
            # 从请求数据中获取要删除的用户消息的 ID
            id = request.data.get("id")
            # 获取数据库连接的游标
            cursor = self.connection.cursor()
            # 构建 SQL 删除语句，从 tt_user_message 表中删除指定 ID 的用户消息记录
            sql = "delete from  tt_user_message where id = {}".format(
                id
            )
            # 执行 SQL 删除语句
            cursor.execute(sql)
            # 提交数据库事务
            self.connection.commit()
            # 构建成功响应结果
            res = {
                'success': True,
                'info': "{}成功".format(title)
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title, request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        except Exception as exp:
            # 记录操作失败的错误信息到日志
            logger.error("{}失败：{}".format(title, str(exp)))
            # 记录异常对象到日志
            logger.error(exp)
            # 记录发生异常所在的文件路径到日志
            logger.error(exp.__traceback__.tb_frame.f_globals["__file__"])
            # 记录发生异常所在的行号到日志
            logger.error(exp.__traceback__.tb_lineno)
            # 构建失败响应结果
            res = {
                'success': False,
                'info': "{}失败：{}".format(title, str(exp))
            }
            # 记录操作结束的时间到日志，格式为年-月-日 时:分:秒
            logger.info("结束时间：" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 记录操作结束的高精度时间
            end = time.perf_counter()
            # 计算操作总共花费的时间
            t = end - start
            # 记录操作总共花费的时间到日志
            logger.info("总共用时{}秒".format(t))
            # 将操作失败的日志信息插入到数据库中
            LoggerHelper.insert_log_info(SysLog, request.auth.user, title + "失败", request.path,
                                         HttpHelper.get_params_request(request),
                                         t, HttpHelper.get_ip_request(request))
        finally:
            # 无论操作成功还是失败，都返回响应结果
            return res
