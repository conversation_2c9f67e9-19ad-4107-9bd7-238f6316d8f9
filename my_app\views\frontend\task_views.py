#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time : 2024-12-26 当前系统时间
# <AUTHOR> zhous
# @Email : <EMAIL>
# @File : task_views.py
# @Descr : 前端任务视图
# @Software: VSCode

from rest_framework import viewsets, mixins
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from my_app.models import TtDroneTasks
from my_app.serializers.frontend.task_serializers import TaskCreateSerializer, TaskReadOnlySerializer
from my_app.serializers.common import CommonResponseSerializer
from my_app.views.base_views import SecureModelViewSet

class TaskFrontendViewSet(SecureModelViewSet, mixins.CreateModelMixin):
    """
    前端任务管理API
    提供任务创建和查询功能
    """
    queryset = TtDroneTasks.objects.all().order_by('-create_time')
    http_method_names = ['get', 'post']  # 只允许查询和创建

    # 配置过滤和排序
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['task_status', 'task_purpose', 'task_urgency', 'selected_drone', 'review_status']
    ordering_fields = ['create_time', 'task_name', 'task_status', 'task_urgency']
    ordering = ['-create_time']

    def get_serializer_class(self):
        """根据操作类型返回对应的序列化器"""
        if self.action == 'create':
            return TaskCreateSerializer
        return TaskReadOnlySerializer
    
    @swagger_auto_schema(
        operation_description="获取任务列表",
        operation_summary="任务列表查询",
        manual_parameters=[
            openapi.Parameter('task_status', openapi.IN_QUERY, description="任务状态", type=openapi.TYPE_STRING),
            openapi.Parameter('task_purpose', openapi.IN_QUERY, description="任务用途", type=openapi.TYPE_STRING),
            openapi.Parameter('task_urgency', openapi.IN_QUERY, description="紧急程度", type=openapi.TYPE_STRING),
            openapi.Parameter('selected_drone', openapi.IN_QUERY, description="执行无人机ID", type=openapi.TYPE_INTEGER),
            openapi.Parameter('review_status', openapi.IN_QUERY, description="审核状态", type=openapi.TYPE_STRING),
            openapi.Parameter('ordering', openapi.IN_QUERY, description="排序字段", type=openapi.TYPE_STRING),
        ],
        responses={
            200: CommonResponseSerializer,
            401: CommonResponseSerializer
        },
        tags=['前端-任务管理']
    )
    def list(self, request, *args, **kwargs):
        """获取任务列表"""
        response = super().list(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="任务列表获取成功"
        ))
    
    @swagger_auto_schema(
        operation_description="获取任务详情",
        operation_summary="任务详情查询",
        responses={
            200: CommonResponseSerializer,
            404: CommonResponseSerializer
        },
        tags=['前端-任务管理']
    )
    def retrieve(self, request, *args, **kwargs):
        """获取任务详情"""
        response = super().retrieve(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="任务详情获取成功"
        ))
    
    @swagger_auto_schema(
        operation_description="创建新任务",
        operation_summary="任务创建",
        request_body=TaskCreateSerializer,
        responses={
            201: CommonResponseSerializer,
            400: CommonResponseSerializer
        },
        tags=['前端-任务管理']
    )
    def create(self, request, *args, **kwargs):
        """创建新任务"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return Response(CommonResponseSerializer.success_response(
                data=serializer.data,
                message="任务创建成功"
            ), status=201)
        return Response(CommonResponseSerializer.error_response(
            message="任务创建失败",
            errors=serializer.errors
        ), status=400)
