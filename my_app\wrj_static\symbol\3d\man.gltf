{"asset": {"version": "2.0", "generator": "babylon.js glTF exporter for 3ds max 2018 v1.3.27"}, "scene": 0, "scenes": [{"nodes": [0, 11], "extensions": {}}], "nodes": [{"children": [1, 4, 7, 9], "mesh": 0, "translation": [-1.49011612e-08, -0.0130780032, 0.008169825], "rotation": [2.910383e-11, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "name": "Box01"}, {"children": [2], "mesh": 1, "translation": [-0.0942113549, 0.8941367, -0.00313891144], "rotation": [0.353553385, -0.353553474, 0.612372458, 0.6123724], "scale": [1.0, 1.0, 1.0], "name": "Cylinder06"}, {"children": [3], "mesh": 2, "translation": [-0.0161913633, 0.0015315339, 0.3926737], "rotation": [-1.47668455e-09, -0.130526155, 1.12165441e-08, 0.9914449], "scale": [0.9999998, 0.9999998, 0.9999999], "name": "Cylinder08"}, {"mesh": 3, "translation": [0.112677455, 0.0103946328, 0.504523754], "rotation": [-1.77635663e-15, 1.11758691e-08, -2.220447e-16, 1.0], "scale": [1.00000024, 1.00000012, 1.00000012], "name": "Cylinder04"}, {"children": [5], "mesh": 4, "translation": [0.08414413, 0.8941367, -0.00313891144], "rotation": [-0.5416752, 0.54167515, -0.454519629, -0.4545194], "scale": [1.0, 1.0, 1.0], "name": "Cylinder05"}, {"children": [6], "mesh": 5, "translation": [-0.0161913484, -0.0138195232, 0.392673582], "rotation": [0.0, -0.173648208, -1.80376026e-15, 0.9848078], "scale": [0.99999994, 1.0, 0.99999994], "name": "Cylinder07"}, {"mesh": 6, "translation": [0.112677395, -0.008177057, 0.504523754], "rotation": [7.215042e-15, 0.1736482, 9.018802e-16, 0.9848078], "scale": [0.99999994, 1.0, 0.99999994], "name": "Cylinder10"}, {"children": [8], "mesh": 7, "translation": [0.1844974, 1.43335843, -0.0382648632], "rotation": [0.415626884, -0.415626973, 0.572061539, 0.5720614], "scale": [1.0, 1.0, 1.0], "name": "Cylinder01"}, {"mesh": 8, "translation": [0.0013397038, -0.06438017, 0.2907442], "rotation": [-8.930709e-16, 0.104528405, -1.56287394e-15, 0.9945219], "scale": [1.0, 1.0, 1.0], "name": "Cylinder03"}, {"children": [10], "mesh": 9, "translation": [-0.184497371, 1.43335843, -0.0382648669], "rotation": [0.5896463, -0.589646339, 0.390278459, 0.390278339], "scale": [1.0, 1.0, 1.0], "name": "Cylinder02"}, {"mesh": 10, "translation": [0.0013397336, 0.0643803, 0.2907442], "rotation": [8.882124e-16, -0.008726528, 0.0, 0.9999619], "scale": [1.0, 1.0, 1.0], "name": "Cylinder09"}, {"translation": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "name": "Default light"}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "TEXCOORD_0": 3}, "indices": 0, "mode": 4, "material": 0}], "name": "Box01"}, {"primitives": [{"attributes": {"POSITION": 5, "NORMAL": 6, "TEXCOORD_0": 7}, "indices": 4, "mode": 4, "material": 0}], "name": "Cylinder06"}, {"primitives": [{"attributes": {"POSITION": 9, "NORMAL": 10, "TEXCOORD_0": 11}, "indices": 8, "mode": 4, "material": 0}], "name": "Cylinder08"}, {"primitives": [{"attributes": {"POSITION": 13, "NORMAL": 14, "TEXCOORD_0": 15}, "indices": 12, "mode": 4, "material": 0}], "name": "Cylinder04"}, {"primitives": [{"attributes": {"POSITION": 17, "NORMAL": 18, "TEXCOORD_0": 19}, "indices": 16, "mode": 4, "material": 0}], "name": "Cylinder05"}, {"primitives": [{"attributes": {"POSITION": 21, "NORMAL": 22, "TEXCOORD_0": 23}, "indices": 20, "mode": 4, "material": 0}], "name": "Cylinder07"}, {"primitives": [{"attributes": {"POSITION": 25, "NORMAL": 26, "TEXCOORD_0": 27}, "indices": 24, "mode": 4, "material": 0}], "name": "Cylinder10"}, {"primitives": [{"attributes": {"POSITION": 29, "NORMAL": 30, "TEXCOORD_0": 31}, "indices": 28, "mode": 4, "material": 0}], "name": "Cylinder01"}, {"primitives": [{"attributes": {"POSITION": 33, "NORMAL": 34, "TEXCOORD_0": 35}, "indices": 32, "mode": 4, "material": 0}], "name": "Cylinder03"}, {"primitives": [{"attributes": {"POSITION": 37, "NORMAL": 38, "TEXCOORD_0": 39}, "indices": 36, "mode": 4, "material": 0}], "name": "Cylinder02"}, {"primitives": [{"attributes": {"POSITION": 41, "NORMAL": 42, "TEXCOORD_0": 43}, "indices": 40, "mode": 4, "material": 0}], "name": "Cylinder09"}], "accessors": [{"bufferView": 0, "componentType": 5123, "count": 4647, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "componentType": 5126, "count": 4647, "max": [0.208098963, 1.789171, 0.178014666], "min": [-0.208098933, 0.8495143, -0.140214667], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 55764, "componentType": 5126, "count": 4647, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "componentType": 5126, "count": 4647, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 9296, "componentType": 5123, "count": 954, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 111528, "componentType": 5126, "count": 954, "max": [0.134084374, 0.093583554, 0.4398567], "min": [-0.11600545, -0.102662392, -0.120039582], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 122976, "componentType": 5126, "count": 954, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 37176, "componentType": 5126, "count": 954, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 11204, "componentType": 5123, "count": 855, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 134424, "componentType": 5126, "count": 855, "max": [0.112677455, 0.0803510547, 0.510011554], "min": [-0.08675435, -0.08289556, -0.06503677], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 144684, "componentType": 5126, "count": 855, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 44808, "componentType": 5126, "count": 855, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 12916, "componentType": 5123, "count": 60, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 154944, "componentType": 5126, "count": 60, "max": [0.100127876, 0.05027426, 0.0003657341], "min": [-2.98023224e-08, -0.0502742827, -0.06948688], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 155664, "componentType": 5126, "count": 60, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 51648, "componentType": 5126, "count": 60, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 13036, "componentType": 5123, "count": 954, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 156384, "componentType": 5126, "count": 954, "max": [0.134084389, 0.08529524, 0.4398568], "min": [-0.116005421, -0.1036508, -0.118922651], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 167832, "componentType": 5126, "count": 954, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 52128, "componentType": 5126, "count": 954, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 14944, "componentType": 5123, "count": 855, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 179280, "componentType": 5126, "count": 855, "max": [0.11267738, 0.08513826, 0.51000917], "min": [-0.08675423, -0.0781301856, -0.06503734], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 189540, "componentType": 5126, "count": 855, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 59760, "componentType": 5126, "count": 855, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 16656, "componentType": 5123, "count": 60, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 199800, "componentType": 5126, "count": 60, "max": [0.100128353, 0.0502711572, 0.000365678221], "min": [0.0, -0.05027117, -0.06950417], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 200520, "componentType": 5126, "count": 60, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 66600, "componentType": 5126, "count": 60, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 16776, "componentType": 5123, "count": 558, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 201240, "componentType": 5126, "count": 558, "max": [0.09722313, 0.052497685, 0.320165753], "min": [-0.079387784, -0.119376868, -0.0759607553], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 207936, "componentType": 5126, "count": 558, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 67080, "componentType": 5126, "count": 558, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 17892, "componentType": 5123, "count": 3084, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 214632, "componentType": 5126, "count": 3084, "max": [0.132605791, 0.0586593747, 0.361923158], "min": [-0.0450504422, -0.107439429, -0.0585205555], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 251640, "componentType": 5126, "count": 3084, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 71544, "componentType": 5126, "count": 3084, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 24060, "componentType": 5123, "count": 558, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 288648, "componentType": 5126, "count": 558, "max": [0.09722316, 0.1193769, 0.320165753], "min": [-0.0793877244, -0.0524976552, -0.0759607553], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 295344, "componentType": 5126, "count": 558, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 96216, "componentType": 5126, "count": 558, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 25176, "componentType": 5123, "count": 3084, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 302040, "componentType": 5126, "count": 3084, "max": [0.132605851, 0.107439414, 0.3619231], "min": [-0.0450504422, -0.0586593747, -0.0585204959], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 339048, "componentType": 5126, "count": 3084, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 100680, "componentType": 5126, "count": 3084, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 3, "componentType": 5126, "count": 19, "max": [1.33333337], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 4, "componentType": 5126, "count": 19, "type": "VEC3", "name": "accessorAnimationPositions"}, {"bufferView": 3, "byteOffset": 76, "componentType": 5126, "count": 15, "max": [1.33333337], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "componentType": 5126, "count": 15, "type": "VEC4", "name": "accessorAnimationRotations"}, {"bufferView": 3, "byteOffset": 136, "componentType": 5126, "count": 27, "max": [1.33333337], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "byteOffset": 240, "componentType": 5126, "count": 27, "type": "VEC4", "name": "accessorAnimationRotations"}, {"bufferView": 3, "byteOffset": 244, "componentType": 5126, "count": 21, "max": [1.33333337], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "byteOffset": 672, "componentType": 5126, "count": 21, "type": "VEC4", "name": "accessorAnimationRotations"}, {"bufferView": 3, "byteOffset": 328, "componentType": 5126, "count": 23, "max": [1.33333337], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "byteOffset": 1008, "componentType": 5126, "count": 23, "type": "VEC4", "name": "accessorAnimationRotations"}, {"bufferView": 3, "byteOffset": 420, "componentType": 5126, "count": 22, "max": [1.33333337], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "byteOffset": 1376, "componentType": 5126, "count": 22, "type": "VEC4", "name": "accessorAnimationRotations"}, {"bufferView": 3, "byteOffset": 508, "componentType": 5126, "count": 17, "max": [1.33333337], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "byteOffset": 1728, "componentType": 5126, "count": 17, "type": "VEC4", "name": "accessorAnimationRotations"}, {"bufferView": 3, "byteOffset": 576, "componentType": 5126, "count": 20, "max": [1.33333337], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "byteOffset": 2000, "componentType": 5126, "count": 20, "type": "VEC4", "name": "accessorAnimationRotations"}, {"bufferView": 3, "byteOffset": 656, "componentType": 5126, "count": 19, "max": [1.33333337], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "byteOffset": 2320, "componentType": 5126, "count": 19, "type": "VEC4", "name": "accessorAnimationRotations"}, {"bufferView": 3, "byteOffset": 732, "componentType": 5126, "count": 22, "max": [1.33333337], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "byteOffset": 2624, "componentType": 5126, "count": 22, "type": "VEC4", "name": "accessorAnimationRotations"}, {"bufferView": 3, "byteOffset": 820, "componentType": 5126, "count": 15, "max": [1.33333337], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "byteOffset": 2976, "componentType": 5126, "count": 15, "type": "VEC4", "name": "accessorAnimationRotations"}], "bufferViews": [{"buffer": 0, "byteLength": 31344, "name": "bufferViewScalar"}, {"buffer": 0, "byteOffset": 31344, "byteLength": 376056, "byteStride": 12, "name": "bufferViewFloatVec3"}, {"buffer": 0, "byteOffset": 407400, "byteLength": 125352, "byteStride": 8, "name": "bufferViewFloatVec2"}, {"buffer": 0, "byteOffset": 532752, "byteLength": 880, "name": "bufferViewAnimationFloatScalar"}, {"buffer": 0, "byteOffset": 533632, "byteLength": 228, "name": "bufferViewAnimationFloatVec3"}, {"buffer": 0, "byteOffset": 533860, "byteLength": 3216, "name": "bufferViewAnimationFloatVec4"}], "buffers": [{"uri": "walk.bin", "byteLength": 537076}], "materials": [{"pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "baseColorTexture": {"index": 0, "texCoord": 0}, "metallicFactor": 0.0, "roughnessFactor": 0.461918056}, "emissiveFactor": [0.0, 0.0, 0.0], "alphaMode": "OPAQUE", "doubleSided": true, "name": "CMan0005"}], "textures": [{"sampler": 0, "source": 0, "name": "CMan0005.jpg"}], "images": [{"uri": "CMan0005.jpg"}], "samplers": [{"magFilter": 9729, "minFilter": 9987, "wrapS": 10497, "wrapT": 10497}], "animations": [{"channels": [{"sampler": 0, "target": {"node": 0, "path": "translation"}}, {"sampler": 1, "target": {"node": 1, "path": "rotation"}}, {"sampler": 2, "target": {"node": 2, "path": "rotation"}}, {"sampler": 3, "target": {"node": 3, "path": "rotation"}}, {"sampler": 4, "target": {"node": 4, "path": "rotation"}}, {"sampler": 5, "target": {"node": 5, "path": "rotation"}}, {"sampler": 6, "target": {"node": 6, "path": "rotation"}}, {"sampler": 7, "target": {"node": 7, "path": "rotation"}}, {"sampler": 8, "target": {"node": 8, "path": "rotation"}}, {"sampler": 9, "target": {"node": 9, "path": "rotation"}}, {"sampler": 10, "target": {"node": 10, "path": "rotation"}}], "samplers": [{"input": 44, "interpolation": "LINEAR", "output": 45}, {"input": 46, "interpolation": "LINEAR", "output": 47}, {"input": 48, "interpolation": "LINEAR", "output": 49}, {"input": 50, "interpolation": "LINEAR", "output": 51}, {"input": 52, "interpolation": "LINEAR", "output": 53}, {"input": 54, "interpolation": "LINEAR", "output": 55}, {"input": 56, "interpolation": "LINEAR", "output": 57}, {"input": 58, "interpolation": "LINEAR", "output": 59}, {"input": 60, "interpolation": "LINEAR", "output": 61}, {"input": 62, "interpolation": "LINEAR", "output": 63}, {"input": 64, "interpolation": "LINEAR", "output": 65}], "name": "All Animations"}]}