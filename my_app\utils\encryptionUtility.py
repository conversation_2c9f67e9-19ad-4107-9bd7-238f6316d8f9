"""
#!/usr/bin/python3.9
# -*- coding: utf-8 -*-
@Project :zwbx_fzcd_service
@File    :encryptionUtility.py
@IDE     :PyCharm
<AUTHOR>
@Date    :2023/8/16 15:43
@Descr:
"""
# 从 vgis_encrption 模块中导入不同的加密工具类和字符串与十六进制转换工具类
from vgis_encrption.encrptionTools import FernetEncryption, RSAEncryption, AESEncryption, StringHexMutualConvertion

# 从项目设置中导入加密相关的配置信息
from my_project.settings import ENCRPTION


class encryptionHelper:
    def __int__(self):
        # 这里可能是拼写错误，通常应该是 __init__ 方法，当前为空，没有实际的初始化操作
        pass

    @staticmethod
    def get_aes_encrytion_object():
        """
        获取 AES 加密对象，该对象的创建涉及多层加密密钥的解密操作。

        具体步骤为：
        1. 使用 Fernet 加密算法对配置中的 key1 进行初始化。
        2. 使用初始化后的 Fernet 加密对象对配置中的 key2 和 key3 进行解密。
        3. 使用解密后的 key2 和 key3 初始化 RSA 加密对象。
        4. 使用 RSA 加密对象对配置中的 key4 进行解密。
        5. 使用解密后的 key4 初始化 AES 加密对象并返回。

        :return: 初始化后的 AES 加密对象
        """
        # 创建 Fernet 加密对象，使用配置中的 key1 并将其编码为字节类型
        fernetEncryption = FernetEncryption(ENCRPTION["key1"].encode())
        # 创建 RSA 加密对象，使用 Fernet 加密对象解密后的 key3 作为私钥，解密后的 key2 作为公钥
        rSAEncryption = RSAEncryption(fernetEncryption.decrypt(ENCRPTION["key3"]), fernetEncryption.decrypt(ENCRPTION["key2"]))
        # 创建 AES 加密对象，使用 RSA 加密对象解密后的 key4 作为密钥
        aESEncryption = AESEncryption(rSAEncryption.decryption(ENCRPTION["key4"]))
        return aESEncryption

    @staticmethod
    def two_layers_encrpt_content(content, aESEncryption):
        """
        对内容进行两层加密操作。

        具体步骤为：
        1. 使用传入的 AES 加密对象对内容进行 AES 加密。
        2. 将加密后的内容从字符串转换为十六进制字符串。

        :param content: 要加密的原始内容
        :param aESEncryption: 用于加密的 AES 加密对象
        :return: 经过两层加密后的十六进制字符串内容
        """
        # 使用 AES 加密对象对内容进行加密
        content_encrpt = aESEncryption.AES_en(content)
        # 将加密后的内容从字符串转换为十六进制字符串
        content_encrpt = StringHexMutualConvertion.convert_str_to_hex(content_encrpt)
        return content_encrpt

    @staticmethod
    def two_layers_descrpt_content(content_encrpt, aESEncryption):
        """
        对经过两层加密的内容进行解密操作。

        具体步骤为：
        1. 将十六进制字符串内容转换为普通字符串。
        2. 使用传入的 AES 加密对象对转换后的字符串进行 AES 解密。

        :param content_encrpt: 经过两层加密的十六进制字符串内容
        :param aESEncryption: 用于解密的 AES 加密对象
        :return: 解密后的原始内容
        """
        # 将十六进制字符串内容转换为普通字符串
        content_descrpt = StringHexMutualConvertion.convert_hex_to_str(content_encrpt)
        # 使用 AES 加密对象对转换后的字符串进行解密
        content_descrpt = aESEncryption.AES_de(content_descrpt)
        return content_descrpt