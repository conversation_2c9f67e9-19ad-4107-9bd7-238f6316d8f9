#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time : 2024-12-26 当前系统时间
# <AUTHOR> zhous
# @Email : <EMAIL>
# @File : schedule_serializers.py
# @Descr : 后台管理航线序列化器
# @Software: VSCode

from rest_framework import serializers
from my_app.models import TtFlightSchedule

class ScheduleAdminSerializer(serializers.ModelSerializer):
    """航线管理序列化器 - 后台管理专用"""

    class Meta:
        model = TtFlightSchedule
        exclude = ['created_by_id', 'updated_by_id']
