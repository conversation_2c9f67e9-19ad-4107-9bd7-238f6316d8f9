# 国际化

class SysInfoEnum:

    #成功
    SUCCESS_CH = '成功'
    SUCCESS_EN = ' is successful'

    #失败
    FAIL_CH = '失败'
    FAIL_EN = ' is fail'

    #请求成功
    REQUEST_SUCCESS_CH = '请求成功'
    REQUEST_SUCCESS_EN = 'request is successful'

    #新增失败
    ADD_FAIL_CH = '新增失败'
    ADD_FAIL_EN = 'add is fail'

    #操作失败
    OPERATE_FAIL_CH = '操作失败'
    OPERATE_FAIL_EN = 'operate is fail'

    #ID是必传参数
    ID_MUST_CH = "ID是必传参数"
    ID_MUST_EN = "ID is required parameter"


    #page 和size 都是必传参数
    PAGE_AND_SIZE_MUST_CH = "page 和size 都是必传参数"
    PAGE_AND_SIZE_MUST_EN = "page and size are required parameters"

    #size 不可以大于
    SIZE_NOT_MORE_THAN_CH = "size 不可以大于"
    SIZE_NOT_MORE_THAN_EN = "size cannot be greater than "

    #page 或者size 不可以大于0
    PAGE_OR_SIZE_NOT_MORE_THAN_CH = "page 或者size 不可以大于0 "
    PAGE_OR_SIZE_NOT_MORE_THAN_EN = "page or size cannot be greater than 0 "


    #查询用户数据一览表
    QUERY_USER_DATA_CH = '查询用户数据一览表'
    QUERY_USER_DATA_EN = 'query user data list'

    #上传矢量文件
    UPLOAD_VECTOR_FILE_CH = "上传矢量文件"
    UPLOAD_VECTOR_FILE_EN = "upload vector file"


    # 导入上传数据到分析列表
    IMPORT_DATA_TO_ANALYSIS_LIST_CH = "导入上传数据到分析列表"
    IMPORT_DATA_TO_ANALYSIS_LIST_EN = "import data to analysis list"

    # 导入服务到分析列表
    IMPORT_SERVICE_TO_ANALYSIS_LIST_CH = "导入服务到分析列表"
    IMPORT_SERVICE_TO_ANALYSIS_LIST_EN = "import service to analysis list"

    # 查询分析数据一览表
    QUERY_ANALYSIS_DATA_LIST_CH = "查询分析数据一览表"
    QUERY_ANALYSIS_DATA_LIST_EN = "query analysis data list"

    #获取分析数据下拉列表
    GET_ANALYSIS_DATA_PULLDOWN_CH = "获取分析数据下拉列表"
    GET_ANALYSIS_DATA_PULLDOWN_EN = "get analysis data pulldown"


    #更新数据名称
    UPDATE_DATA_NAME_CH = "更新数据名称"
    UPDATE_DATA_NAME_EN = "update data name"


    # 更新的数据名称:{}已存在，请换个名称
    UPDATE_DATA_NAME_EXIST_CH = "更新的数据名称:{}已存在，请换个名称"
    UPDATE_DATA_NAME_EXIST_EN = "the updated data name:{} already exists, please change the name"

    #更新数据里的主键ID号:{}不存在，请换个ID号
    UPDATE_DATA_ID_NOT_EXIST_CH = "更新数据里的主键ID号:{}不存在，请换个ID号"
    UPDATE_DATA_ID_NOT_EXIST_EN = "the primary key ID number:{} in the updated data does not exist, please change the ID number"

    #{}失败，可能原因是{}
    FAIL_REASON_CH = "{}失败，可能原因是{}"
    FAIL_REASON_EN = "{} failed, the possible reason is {}"

    #上传文件
    UPLOAD_FILE_CH = "上传文件"
    UPLOAD_FILE_EN = "upload file"



    #上传TIF文件
    UPLOAD_TIF_FILE_CH = "上传TIF文件"
    UPLOAD_TIF_FILE_EN = "upload tif file"

    #请选择要上传的文件
    PLEASE_SELECT_FILE_TO_UPLOAD_CH = "请选择要上传的文件"
    PLEASE_SELECT_FILE_TO_UPLOAD_EN = "please select the file to upload"

    #上传文件成功
    UPLOAD_FILE_SUCCESS_CH = "上传文件成功"
    UPLOAD_FILE_SUCCESS_EN = "upload file success"

    #可导入到分析列表中的数据查询
    QUERY_DATA_TO_ANALYSIS_LIST_CH = "可导入到分析列表中的数据查询"
    QUERY_DATA_TO_ANALYSIS_LIST_EN = "query data to analysis list"

    #下载数据
    DOWNLOAD_DATA_CH = "下载数据"
    DOWNLOAD_DATA_EN = "download data"

    #删除数据
    DELETE_DATA_CH = "删除数据"
    DELETE_DATA_EN = "delete data"

    #查询模型列表
    QUERY_MODEL_LIST_CH = "查询模型列表"
    QUERY_MODEL_LIST_EN = "query model list"

    #查询产品列表
    QUERY_PRODUCT_LIST_CH = "查询产品列表"
    QUERY_PRODUCT_LIST_EN = "query product list"

    #查询卫星列表
    QUERY_SATELLITE_LIST_CH = "查询卫星列表"
    QUERY_SATELLITE_LIST_EN = "query satellite list"

    #获取卫星数据详情信息
    GET_SATELLITE_DATA_DETAIL_CH = "获取卫星数据详情信息"
    GET_SATELLITE_DATA_DETAIL_EN = "get satellite data detail"

    #{}失败，没有传卫星id
    FAIL_REASON_NO_SATELLITE_ID_CH = "{}失败，没有传卫星id"
    FAIL_REASON_NO_SATELLITE_ID_EN = "{} failed, no satellite id"

    #可导入到分析列表中的服务查询
    QUERY_SERVICE_TO_ANALYSIS_LIST_CH = "可导入到分析列表中的服务查询"
    QUERY_SERVICE_TO_ANALYSIS_LIST_EN = "query service to analysis list"

    #创建分析任务
    CREATE_ANALYSIS_TASK_CH = "创建分析任务"
    CREATE_ANALYSIS_TASK_EN = "create analysis task"

    #变化检测的两个数据源投影必须相同
    TWO_DATA_SOURCE_PROJECTION_MUST_SAME_CH = "变化检测的两个数据源投影必须相同"
    TWO_DATA_SOURCE_PROJECTION_MUST_SAME_EN = "the projection of the two data sources for change detection must be the same"

    #需要选择提取范围
    NEED_SELECT_EXTRACTION_RANGE_CH = "需要选择提取范围"
    NEED_SELECT_EXTRACTION_RANGE_EN = "need select extraction range"

    #提取范围超过50平方公里，请重新选择
    EXTRACTION_RANGE_EXCEED_50_SQUARE_KM_CH = "提取范围超过50平方公里，请重新选择"
    EXTRACTION_RANGE_EXCEED_50_SQUARE_KM_EN = "extraction range exceed 50 square km, please reselect"

    #只能上传面状要素
    ONLY_UPLOAD_POLYGON_FEATURE_CH = "只能上传面状要素"
    ONLY_UPLOAD_POLYGON_FEATURE_EN = "only upload polygon feature"

    #只能上传一个矢量要素
    ONLY_UPLOAD_ONE_VECTOR_FEATURE_CH = "只能上传一个矢量要素"
    ONLY_UPLOAD_ONE_VECTOR_FEATURE_EN = "only upload one vector feature"

    #任务执行失败
    TASK_EXECUTION_FAILED_CH = "任务执行失败"
    TASK_EXECUTION_FAILED_EN = "task execution failed"

    #任务执行结果为空
    TASK_EXECUTION_RESULT_IS_EMPTY_CH = "任务执行结果为空"
    TASK_EXECUTION_RESULT_IS_EMPTY_EN = "task execution result is empty"

    #任务执行成功
    TASK_EXECUTION_SUCCESS_CH = "任务执行成功"
    TASK_EXECUTION_SUCCESS_EN = "task execution success"

    #获取任务执行进度
    GET_TASK_EXECUTION_PROGRESS_CH = "获取任务执行进度"
    GET_TASK_EXECUTION_PROGRESS_EN = "get task execution progress"

    #获取全国的分地区分省数据失败
    GET_NATIONAL_DATA_BY_PROVINCE_FAILED_CH = "获取全国的分地区分省数据失败"
    GET_NATIONAL_DATA_BY_PROVINCE_FAILED_EN = "get national data by province failed"

    #通过省份获取地市数据失败
    GET_NATIONAL_DATA_BY_CITY_FAILED_CH = "通过省份获取地市数据失败"
    GET_NATIONAL_DATA_BY_CITY_FAILED_EN = "get national data by city failed"

    #通过地市获取区县数据失败
    GET_NATIONAL_DATA_BY_COUNTY_FAILED_CH = "通过地市获取区县数据失败"
    GET_NATIONAL_DATA_BY_COUNTY_FAILED_EN = "get national data by county failed"


