# Generated by Django 4.2.2 on 2025-08-26 16:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my_app', '0004_ttdronealerts_alert_geojson_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='ttdronealerts',
            name='create_user_id',
        ),
        migrations.RemoveField(
            model_name='ttdronealerts',
            name='update_user_id',
        ),
        migrations.RemoveField(
            model_name='ttdronebasicinfo',
            name='create_user_id',
        ),
        migrations.RemoveField(
            model_name='ttdronebasicinfo',
            name='update_user_id',
        ),
        migrations.RemoveField(
            model_name='ttdronetasks',
            name='create_user_id',
        ),
        migrations.RemoveField(
            model_name='ttdronetasks',
            name='shenh',
        ),
        migrations.RemoveField(
            model_name='ttdronetasks',
            name='update_user_id',
        ),
        migrations.AddField(
            model_name='ttdronealerts',
            name='created_by_id',
            field=models.BigIntegerField(blank=True, db_comment='创建用户ID', null=True),
        ),
        migrations.AddField(
            model_name='ttdronealerts',
            name='updated_by_id',
            field=models.BigIntegerField(blank=True, db_comment='更新用户ID', null=True),
        ),
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='created_by_id',
            field=models.BigIntegerField(blank=True, db_comment='创建用户ID', null=True),
        ),
        migrations.AddField(
            model_name='ttdronebasicinfo',
            name='updated_by_id',
            field=models.BigIntegerField(blank=True, db_comment='更新用户ID', null=True),
        ),
        migrations.AddField(
            model_name='ttdronetasks',
            name='created_by_id',
            field=models.BigIntegerField(blank=True, db_comment='创建用户ID', null=True),
        ),
        migrations.AddField(
            model_name='ttdronetasks',
            name='updated_by_id',
            field=models.BigIntegerField(blank=True, db_comment='更新用户ID', null=True),
        ),
        migrations.AddField(
            model_name='ttflightschedule',
            name='created_by_id',
            field=models.BigIntegerField(blank=True, db_comment='创建用户ID', null=True),
        ),
        migrations.AddField(
            model_name='ttflightschedule',
            name='updated_by_id',
            field=models.BigIntegerField(blank=True, db_comment='更新用户ID', null=True),
        ),
        migrations.AlterField(
            model_name='ttdronetasks',
            name='reviewer_id',
            field=models.BigIntegerField(blank=True, db_comment='审核人ID', null=True),
        ),
        migrations.AlterField(
            model_name='ttdronetasks',
            name='task_shape',
            field=models.CharField(choices=[('Point', '点'), ('LineString', '线'), ('Circle', '圆形'), ('Polygon', '多边形'), ('Rectangle', '正北矩形')], db_comment='任务区域的几何形状类型', default='Polygon', max_length=10),
        ),
    ]
