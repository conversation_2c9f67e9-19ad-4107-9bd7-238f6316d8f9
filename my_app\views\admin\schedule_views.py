#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time : 2024-12-26 当前系统时间
# <AUTHOR> zhous
# @Email : <EMAIL>
# @File : schedule_views.py
# @Descr : 后台管理航线视图
# @Software: VSCode

from rest_framework import viewsets
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from my_app.models import TtFlightSchedule
from my_app.serializers.admin.schedule_serializers import ScheduleAdminSerializer
from my_app.serializers.common import CommonResponseSerializer
from my_app.views.base_views import SecureModelViewSet

class ScheduleAdminViewSet(SecureModelViewSet):
    """
    后台管理航线管理API
    提供完整的航线管理功能
    """
    queryset = TtFlightSchedule.objects.all().order_by('-create_time')
    serializer_class = ScheduleAdminSerializer

    # 配置过滤和排序
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['schedule_status', 'drone', 'task']
    ordering_fields = ['create_time', 'schedule_status']
    ordering = ['-create_time']
    
    def get_permissions(self):
        """确保只有管理员能访问"""
        permissions = super().get_permissions()
        if not (self.request.user.is_staff or self.request.user.is_superuser):
            self.permission_denied(self.request, message="需要管理员权限")
        return permissions
    
    @swagger_auto_schema(
        operation_description="获取航线列表",
        operation_summary="航线列表管理",
        manual_parameters=[
            openapi.Parameter('schedule_status', openapi.IN_QUERY, description="航线状态", type=openapi.TYPE_STRING),
            openapi.Parameter('drone', openapi.IN_QUERY, description="关联无人机ID", type=openapi.TYPE_INTEGER),
            openapi.Parameter('task', openapi.IN_QUERY, description="关联任务ID", type=openapi.TYPE_INTEGER),
            openapi.Parameter('ordering', openapi.IN_QUERY, description="排序字段", type=openapi.TYPE_STRING),
        ],
        responses={200: CommonResponseSerializer},
        tags=['后台管理-航线管理']
    )
    def list(self, request, *args, **kwargs):
        """获取航线列表"""
        response = super().list(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="航线列表获取成功"
        ))

    @swagger_auto_schema(
        operation_description="获取航线详情",
        operation_summary="航线详情管理",
        responses={200: CommonResponseSerializer, 404: CommonResponseSerializer},
        tags=['后台管理-航线管理']
    )
    def retrieve(self, request, *args, **kwargs):
        """获取航线详情"""
        response = super().retrieve(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="航线详情获取成功"
        ))
    
    @swagger_auto_schema(
        operation_description="创建航线",
        operation_summary="航线创建",
        request_body=ScheduleAdminSerializer,
        responses={201: CommonResponseSerializer},
        tags=['后台管理-航线管理']
    )
    def create(self, request, *args, **kwargs):
        """创建航线"""
        response = super().create(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="航线创建成功"
        ), status=201)
    
    @swagger_auto_schema(
        operation_description="更新航线信息",
        operation_summary="航线信息更新",
        request_body=ScheduleAdminSerializer,
        responses={200: CommonResponseSerializer},
        tags=['后台管理-航线管理']
    )
    def update(self, request, *args, **kwargs):
        """更新航线信息"""
        response = super().update(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="航线更新成功"
        ))

    @swagger_auto_schema(
        operation_description="部分更新航线信息",
        operation_summary="航线部分更新",
        request_body=ScheduleAdminSerializer,
        responses={200: CommonResponseSerializer, 400: CommonResponseSerializer},
        tags=['后台管理-航线管理']
    )
    def partial_update(self, request, *args, **kwargs):
        """部分更新航线信息"""
        response = super().partial_update(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            data=response.data,
            message="航线部分更新成功"
        ))
    
    @swagger_auto_schema(
        operation_description="删除航线",
        operation_summary="航线删除",
        responses={204: CommonResponseSerializer},
        tags=['后台管理-航线管理']
    )
    def destroy(self, request, *args, **kwargs):
        """删除航线"""
        super().destroy(request, *args, **kwargs)
        return Response(CommonResponseSerializer.success_response(
            message="航线删除成功"
        ), status=204)
