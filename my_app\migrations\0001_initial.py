# Generated by Django 4.2.2 on 2025-08-25 18:33

from decimal import Decimal
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AuthGroup',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=150, unique=True)),
            ],
            options={
                'db_table': 'auth_group',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AuthGroupPermissions',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'db_table': 'auth_group_permissions',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AuthPermission',
            fields=[
                ('id', models.<PERSON><PERSON>ield(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('codename', models.CharField(max_length=100)),
            ],
            options={
                'db_table': 'auth_permission',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AuthUser',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('password', models.CharField(db_comment='用户密码', max_length=128)),
                ('last_login', models.DateTimeField(blank=True, db_comment='上次登录时间', null=True)),
                ('is_superuser', models.BooleanField(db_comment='是否是超级用户')),
                ('username', models.CharField(blank=True, db_comment='完整姓名', max_length=150, null=True)),
                ('first_name', models.CharField(blank=True, db_comment='名字', max_length=150, null=True)),
                ('last_name', models.CharField(blank=True, db_comment='姓氏', max_length=150, null=True)),
                ('email', models.CharField(blank=True, db_comment='邮箱', max_length=254, null=True)),
                ('is_staff', models.BooleanField(blank=True, db_comment='是否是职员', null=True)),
                ('is_active', models.BooleanField(blank=True, db_comment='账户是否激活', null=True)),
                ('date_joined', models.DateTimeField(blank=True, db_comment='账户创建时间', null=True)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建者用户id', null=True)),
                ('department_id', models.BigIntegerField(blank=True, db_comment='部门id', null=True)),
                ('fullname', models.CharField(blank=True, db_comment='完整姓名', max_length=255, null=True)),
                ('login_error_attempts', models.SmallIntegerField(blank=True, db_comment='登录错误次数', null=True)),
                ('login_locked_until', models.DateTimeField(blank=True, db_comment='登录锁定时间', null=True)),
                ('mobile', models.CharField(blank=True, db_comment='电话', max_length=100, null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='修改时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='修改者用户id', null=True)),
                ('sex', models.CharField(blank=True, db_comment='性别', max_length=255, null=True)),
                ('status', models.IntegerField(blank=True, db_comment='状态，1正常，0 禁用', null=True)),
                ('age', models.IntegerField(blank=True, db_comment='年龄', null=True)),
                ('educational_background', models.CharField(blank=True, db_comment='教育背景', max_length=100, null=True)),
                ('ethnicity', models.CharField(blank=True, db_comment='所属民族', max_length=100, null=True)),
                ('id_card', models.CharField(blank=True, db_comment='身份证号码', max_length=20, null=True)),
                ('template_id', models.BigIntegerField(blank=True, db_comment='模板id', null=True)),
            ],
            options={
                'db_table': 'auth_user',
                'db_table_comment': '系统用户表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AuthUserGroups',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'db_table': 'auth_user_groups',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AuthUserUserPermissions',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'db_table': 'auth_user_user_permissions',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DjangoAdminLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_time', models.DateTimeField()),
                ('object_id', models.TextField(blank=True, null=True)),
                ('object_repr', models.CharField(max_length=200)),
                ('action_flag', models.SmallIntegerField()),
                ('change_message', models.TextField()),
            ],
            options={
                'db_table': 'django_admin_log',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DjangoContentType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('app_label', models.CharField(max_length=100)),
                ('model', models.CharField(max_length=100)),
            ],
            options={
                'db_table': 'django_content_type',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DjangoMigrations',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('app', models.CharField(max_length=255)),
                ('name', models.CharField(max_length=255)),
                ('applied', models.DateTimeField()),
            ],
            options={
                'db_table': 'django_migrations',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DjangoSession',
            fields=[
                ('session_key', models.CharField(max_length=40, primary_key=True, serialize=False)),
                ('session_data', models.TextField()),
                ('expire_date', models.DateTimeField()),
            ],
            options={
                'db_table': 'wjyy_gz_django_session',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RencheDict',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('mc', models.CharField(blank=True, max_length=255, null=True)),
                ('long', models.CharField(blank=True, max_length=255, null=True)),
                ('width', models.CharField(blank=True, max_length=255, null=True)),
                ('high', models.CharField(blank=True, max_length=255, null=True)),
                ('load', models.CharField(blank=True, max_length=255, null=True)),
                ('area', models.FloatField(blank=True, null=True)),
                ('type', models.SmallIntegerField(blank=True, null=True)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user', models.CharField(blank=True, max_length=255, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'renche_dict',
                'db_table_comment': '人车面积',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysConfig',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('param_key', models.CharField(blank=True, max_length=50, null=True)),
                ('param_value', models.CharField(blank=True, max_length=2000, null=True)),
                ('status', models.IntegerField(blank=True, null=True)),
                ('remark', models.CharField(blank=True, max_length=500, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_CONFIG',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysDepartment',
            fields=[
                ('department_id', models.BigAutoField(primary_key=True, serialize=False)),
                ('department_name', models.CharField(blank=True, max_length=128, null=True)),
                ('parent_id', models.BigIntegerField(blank=True, null=True)),
                ('state', models.CharField(blank=True, max_length=1, null=True)),
                ('state_date', models.DateField(blank=True, null=True)),
                ('order_num', models.BigIntegerField(blank=True, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('del_flag', models.IntegerField(blank=True, null=True)),
                ('master', models.CharField(blank=True, max_length=255, null=True)),
                ('tel', models.CharField(blank=True, max_length=255, null=True)),
                ('email', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_DEPARTMENT',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysLog',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('username', models.CharField(blank=True, max_length=50, null=True)),
                ('operation', models.CharField(blank=True, max_length=50, null=True)),
                ('method', models.CharField(blank=True, max_length=200, null=True)),
                ('params', models.CharField(blank=True, max_length=5000, null=True)),
                ('time', models.FloatField()),
                ('ip', models.CharField(blank=True, max_length=64, null=True)),
                ('create_date', models.DateTimeField(blank=True, null=True)),
                ('error_info', models.TextField(blank=True, null=True)),
                ('log_type', models.BigIntegerField(blank=True, db_comment='日志类型：0:登录日志，1：操作日志，2：异常日志', null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_LOG',
                'db_table_comment': '系统日志表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysMenu',
            fields=[
                ('menu_id', models.BigAutoField(primary_key=True, serialize=False)),
                ('parent_id', models.BigIntegerField(blank=True, null=True)),
                ('name', models.CharField(blank=True, max_length=50, null=True)),
                ('url', models.CharField(blank=True, max_length=200, null=True)),
                ('perms', models.CharField(blank=True, max_length=500, null=True)),
                ('type', models.IntegerField(blank=True, null=True)),
                ('icon', models.CharField(blank=True, max_length=50, null=True)),
                ('order_num', models.IntegerField(blank=True, null=True)),
                ('is_show', models.CharField(blank=True, max_length=1, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_MENU',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysModelRuls',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user', models.CharField(blank=True, max_length=255, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
                ('moban_title', models.CharField(blank=True, db_comment='模板标题', max_length=255, null=True)),
                ('moban_data', models.TextField(blank=True, db_comment='模板概述', null=True)),
                ('disable', models.BigIntegerField(blank=True, db_comment='是否禁用：0：启用，1：禁用，同类型的模板只能启动一个', null=True)),
                ('moban_type', models.CharField(blank=True, db_comment='模板类型', max_length=255, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_MODEL_RULS',
                'db_table_comment': '后台管理_模板概述编辑',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysModelRulsFields',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('title', models.CharField(blank=True, db_comment='模板名称', max_length=255, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user', models.CharField(blank=True, max_length=255, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
                ('fields', models.TextField(blank=True, db_comment='模板对应字段的json', null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_MODEL_RULS_FIELDS',
                'db_table_comment': '后台管理_模板概述插入的字段',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysModelWeightTemplate',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('title', models.CharField(blank=True, max_length=255, null=True)),
                ('is_active', models.BooleanField(blank=True, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user', models.CharField(blank=True, max_length=255, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
                ('status', models.BooleanField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_MODEL_WEIGHT_TEMPLATE',
                'db_table_comment': '后台管理_模型权重模板管理',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysOss',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('url', models.CharField(blank=True, max_length=200, null=True)),
                ('create_date', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_OSS',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysParam',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('param_en_key', models.CharField(db_comment='参数键英文名称', max_length=200)),
                ('param_cn_key', models.CharField(db_comment='参数键中文名称', max_length=200)),
                ('param_value', models.CharField(db_comment='参数键值', max_length=255)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('update_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_PARAM',
                'db_table_comment': '系统参数表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysRole',
            fields=[
                ('role_id', models.BigAutoField(primary_key=True, serialize=False)),
                ('role_name', models.CharField(blank=True, max_length=100, null=True)),
                ('remark', models.CharField(blank=True, max_length=100, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_ROLE',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysRoleMenu',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('role_id', models.BigIntegerField(blank=True, null=True)),
                ('menu_id', models.BigIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_ROLE_MENU',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysUser',
            fields=[
                ('user_id', models.BigAutoField(primary_key=True, serialize=False)),
                ('username', models.CharField(max_length=50)),
                ('password', models.CharField(blank=True, max_length=100, null=True)),
                ('salt', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.CharField(blank=True, max_length=100, null=True)),
                ('mobile', models.CharField(blank=True, max_length=100, null=True)),
                ('status', models.IntegerField(blank=True, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('department_id', models.BigIntegerField(blank=True, null=True)),
                ('sex', models.CharField(blank=True, max_length=255, null=True)),
                ('fullname', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'db_table': 'sys_user',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysUserRole',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('user_id', models.BigIntegerField(blank=True, null=True)),
                ('role_id', models.BigIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_USER_ROLE',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='SysUserToken',
            fields=[
                ('user_id', models.BigAutoField(primary_key=True, serialize=False)),
                ('token', models.CharField(max_length=100)),
                ('expire_time', models.DateTimeField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_USER_TOKEN',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TmBingtuan',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('dis_name', models.CharField(blank=True, max_length=255, null=True)),
                ('dis_code', models.IntegerField(blank=True, null=True)),
                ('parent_code', models.IntegerField(blank=True, null=True)),
                ('type', models.SmallIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'tm_bingtuan',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TmDdistrict',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('dis_name', models.CharField(blank=True, max_length=255, null=True)),
                ('dis_code', models.IntegerField(blank=True, null=True)),
                ('parent_code', models.IntegerField(blank=True, null=True)),
                ('type', models.SmallIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_TM_DISTRICT',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TmRegion',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('dis_name', models.CharField(blank=True, max_length=255, null=True)),
                ('dis_code', models.IntegerField(blank=True, null=True)),
                ('parent_code', models.IntegerField(blank=True, null=True)),
                ('type', models.SmallIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'tm_region',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtAnnotationSysmbolData',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('cname', models.CharField(db_comment='符号中文名称', max_length=100)),
                ('ename', models.CharField(db_comment='符号英文名称', max_length=100)),
                ('type', models.BigIntegerField(blank=True, db_comment='标注符号类型：1 军标标注 2 图元标注 3 三维模型 4 自定义标注', null=True)),
                ('remark', models.CharField(db_comment='符号描述', max_length=2000)),
                ('sysmbol_path', models.CharField(db_comment='标注符号相对路径', max_length=255)),
                ('td_sysmbol', models.CharField(db_comment='3D符号相对路径', max_length=255)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'tt_annotation_sysmbol_data',
                'db_table_comment': '标注符号数据表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtAnnotationType',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('annotation_ename', models.CharField(db_comment='标注类型英文名称', max_length=100)),
                ('annotation_cname', models.CharField(db_comment='标注类型中文名称', max_length=100)),
                ('remark', models.CharField(db_comment='备注信息', max_length=255)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'tt_annotation_type',
                'db_table_comment': '标注类型字典表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtDronAlertFileData',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('alert_id', models.BigIntegerField(blank=True, db_comment='告警id', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('file_size', models.DecimalField(blank=True, decimal_places=0, max_digits=24, null=True)),
                ('file_name', models.CharField(blank=True, max_length=255, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('file_suffix', models.CharField(blank=True, max_length=255, null=True)),
                ('path', models.CharField(blank=True, max_length=512, null=True)),
            ],
            options={
                'db_table': 'tt_dron_alert_file_data',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtDronBaskFileData',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('file_size', models.DecimalField(blank=True, decimal_places=0, max_digits=24, null=True)),
                ('file_name', models.CharField(blank=True, max_length=255, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('file_suffix', models.CharField(blank=True, max_length=255, null=True)),
                ('path', models.CharField(blank=True, max_length=512, null=True)),
            ],
            options={
                'db_table': 'tt_dron_bask_file_data',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtFeatureAnnotationData',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('name', models.CharField(db_comment='标注名称', max_length=100)),
                ('type', models.BigIntegerField(blank=True, db_comment='标注类型：1 军标标注 2 图元标注 3 三维模型 4 自定义标注', null=True)),
                ('type_name', models.CharField(db_comment='标注类型名称', max_length=100)),
                ('point_type', models.BigIntegerField(blank=True, db_comment='点类型：1 地理要素 2 重要目标 3 战场事件 4 战略支援点', null=True)),
                ('point_type_name', models.CharField(db_comment='点类型名称', max_length=100)),
                ('remark', models.CharField(db_comment='标注描述', max_length=2000, null=True)),
                ('friend_foe_information', models.BigIntegerField(blank=True, db_comment='敌我信息：0 全部 1我方，2敌方 3 友邻 4 中立 5  不明', null=True)),
                ('friend_foe_information_name', models.CharField(db_comment='敌我信息名称', max_length=100)),
                ('intelligence_source', models.BigIntegerField(blank=True, db_comment='情报来源：0 全部 1 技术/武器侦查 2 无线电侦听 3 无人机 4 各级通报 5 观察 6 捕获 7 其他', null=True)),
                ('intelligence_source_name', models.CharField(db_comment='情报来源名称', max_length=100)),
                ('lng', models.FloatField(blank=True, db_comment='经度', null=True)),
                ('lat', models.FloatField(blank=True, db_comment='纬度', null=True)),
                ('elv', models.FloatField(blank=True, db_comment='海拔', null=True)),
                ('annotation_sysmbol_id', models.BigIntegerField(blank=True, db_comment='标注符号编号', null=True)),
                ('annotation_sysmbol_style', models.TextField(blank=True, db_comment='情报来源：0 全部 1 技术/武器侦查 2 无线电侦听 3 无人机 4 各级通报 5 观察 6 捕获 7 其他', null=True)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'tt_feature_annotation_data',
                'db_table_comment': '要素标注数据表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtFeatureAnnotationeNewFields',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('annotation_id', models.BigIntegerField(blank=True, db_comment='标注数据ID', null=True)),
                ('new_fields_ename', models.CharField(db_comment='标注数据新增字段的英文名拼接字符串', max_length=100)),
                ('new_fields_cname', models.CharField(db_comment='标注数据新增字段的中文名拼接字符串', max_length=100)),
                ('new_fields_type', models.CharField(db_comment='标注数据新增字段的字段类型拼接字符串', max_length=100)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'tt_feature_annotatione_new_fields',
                'db_table_comment': '标注数据新增字段表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtFieldType',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('type_ename', models.CharField(db_comment='字段类型英文名', max_length=100)),
                ('type_cname', models.CharField(db_comment='字段类型中文名', max_length=100)),
                ('remark', models.CharField(db_comment='备注信息', max_length=255)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'tt_field_type',
                'db_table_comment': '字段类型字典表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtFriendFoeInfo',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('info_ename', models.CharField(db_comment='信息英文名称', max_length=100)),
                ('info_cname', models.CharField(db_comment='信息中文名称', max_length=100)),
                ('remark', models.CharField(db_comment='备注信息', max_length=255)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'tt_friend_foe_info',
                'db_table_comment': '敌我信息字典表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtGlwDlhjfx',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('mc', models.CharField(blank=True, max_length=255, null=True)),
                ('event_data', models.TextField(blank=True, null=True)),
                ('baogao', models.TextField(blank=True, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user', models.CharField(blank=True, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_DLHJFX',
                'db_table_comment': '地理环境分析',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtGlwGcwzfx',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('mc', models.CharField(blank=True, max_length=255, null=True)),
                ('rwfw', models.CharField(blank=True, max_length=255, null=True)),
                ('start_point', models.CharField(blank=True, max_length=255, null=True)),
                ('rwbj', models.CharField(blank=True, max_length=255, null=True)),
                ('people_number', models.BigIntegerField(blank=True, null=True)),
                ('baogao', models.TextField(blank=True, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user', models.CharField(blank=True, max_length=255, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
                ('sjbj', models.CharField(blank=True, max_length=255, null=True)),
                ('rwtu', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_GCWZFX',
                'db_table_comment': '观察位置',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtGlwJjdy',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('mc', models.CharField(blank=True, max_length=255, null=True)),
                ('rwfw', models.CharField(blank=True, max_length=255, null=True)),
                ('start_point', models.CharField(blank=True, max_length=255, null=True)),
                ('rwbj', models.CharField(blank=True, max_length=255, null=True)),
                ('sjbj', models.CharField(blank=True, max_length=255, null=True)),
                ('rwtu', models.CharField(blank=True, max_length=255, null=True)),
                ('lists', models.CharField(blank=True, max_length=255, null=True)),
                ('baogao', models.TextField(blank=True, null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('update_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_JJDY',
                'db_table_comment': '集结地域分析',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtGlwPointMark',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('title', models.CharField(blank=True, max_length=255, null=True)),
                ('type_str', models.CharField(blank=True, db_comment='关键点类型中文:类型1，类型2，等', max_length=255, null=True)),
                ('type_index', models.BigIntegerField(blank=True, db_comment='关键点类型序号。1：类型1，2：类型2，等', null=True)),
                ('icon_str', models.CharField(blank=True, db_comment='关键点位的图标的路径字符串', max_length=255, null=True)),
                ('icon_index', models.BigIntegerField(blank=True, db_comment='关键点位的图标序号', null=True)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user', models.CharField(blank=True, max_length=255, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_POINT_MARK',
                'db_table_comment': '人工点位标注',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtGlwQsfx',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('mc', models.CharField(blank=True, max_length=255, null=True)),
                ('rwtu', models.TextField(blank=True, null=True)),
                ('baogao', models.TextField(blank=True, null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('update_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_QSFX',
                'db_table_comment': '驱散方向',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtGlwWwfk',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('mc', models.CharField(blank=True, max_length=255, null=True)),
                ('event_data', models.TextField(blank=True, null=True)),
                ('baogao', models.TextField(blank=True, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user', models.CharField(blank=True, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_WWFK',
                'db_table_comment': '外围封控',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtGlwZhbzfx',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('mc', models.CharField(blank=True, max_length=255, null=True)),
                ('rwfw', models.CharField(blank=True, max_length=255, null=True)),
                ('start_point', models.CharField(blank=True, max_length=255, null=True)),
                ('rwbj', models.CharField(blank=True, max_length=255, null=True)),
                ('sjbj', models.CharField(blank=True, max_length=255, null=True)),
                ('rwtu', models.CharField(blank=True, max_length=255, null=True)),
                ('baogao', models.TextField()),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('update_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_ZHBZFX',
                'db_table_comment': '综合保障分析',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtGlwZhfx',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('mc', models.CharField(blank=True, max_length=255, null=True)),
                ('baogao', models.JSONField(blank=True, null=True)),
                ('sjbj', models.CharField(blank=True, max_length=255, null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('update_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_ZHFX',
                'db_table_comment': '综合分析表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtGlwZymbfx',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('mc', models.CharField(blank=True, max_length=255, null=True)),
                ('rwfw', models.CharField(blank=True, max_length=255, null=True)),
                ('start_point', models.CharField(blank=True, max_length=255, null=True)),
                ('rwbj', models.CharField(blank=True, max_length=255, null=True)),
                ('sjbj', models.CharField(blank=True, max_length=255, null=True)),
                ('rwtu', models.CharField(blank=True, max_length=255, null=True)),
                ('baogao', models.TextField(blank=True, null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('update_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_ZYMBFX',
                'db_table_comment': '重要目标分析',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtIntelligenceSource',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('source_ename', models.CharField(db_comment='来源英文名称', max_length=100)),
                ('source_cname', models.CharField(db_comment='来源中文名称', max_length=100)),
                ('remark', models.CharField(db_comment='备注信息', max_length=255)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'tt_intelligence_source',
                'db_table_comment': '情报来源字典表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtKeyPointAreaData',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('lng', models.FloatField(blank=True, db_comment='经度', null=True)),
                ('lat', models.FloatField(blank=True, db_comment='纬度', null=True)),
                ('name', models.CharField(db_comment='名称', max_length=255)),
                ('address', models.CharField(db_comment='地址', max_length=255)),
                ('province', models.CharField(db_comment='省份', max_length=2550)),
                ('city', models.CharField(db_comment='地市', max_length=2550)),
                ('area', models.CharField(db_comment='区县', max_length=2550)),
                ('poi_type', models.CharField(db_comment='图层类型，医院，学校', max_length=2550)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
                ('doc_url', models.CharField(db_comment='报告url地址', max_length=2550)),
            ],
            options={
                'db_table': 'tt_key_point_area',
                'db_table_comment': '重点点位区域数据表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtPoiData',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('lng', models.FloatField(blank=True, db_comment='经度', null=True)),
                ('lat', models.FloatField(blank=True, db_comment='纬度', null=True)),
                ('name', models.CharField(db_comment='名称', max_length=255)),
                ('address', models.CharField(db_comment='地址', max_length=255)),
                ('province', models.CharField(db_comment='省份', max_length=2550)),
                ('city', models.CharField(db_comment='地市', max_length=2550)),
                ('area', models.CharField(db_comment='区县', max_length=2550)),
                ('poi_type', models.CharField(db_comment='图层类型，医院，学校', max_length=2550)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'tt_poi_data',
                'db_table_comment': 'POI数据表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtPointType',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('point_ename', models.CharField(db_comment='点类型英文名称', max_length=100)),
                ('point_cname', models.CharField(db_comment='点类型中文名称', max_length=100)),
                ('remark', models.CharField(db_comment='备注信息', max_length=255)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'tt_point_type',
                'db_table_comment': '标注点类型字典表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtSelectDict',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('cname', models.CharField(db_comment='中文名称', max_length=100)),
                ('ename', models.CharField(db_comment='英文名称', max_length=100)),
                ('type', models.BigIntegerField(blank=True, db_comment='类型', null=True)),
                ('remark', models.CharField(db_comment='描述', max_length=2000)),
                ('sysmbol_path', models.CharField(db_comment='标注符号相对路径', max_length=255)),
                ('td_sysmbol', models.CharField(db_comment='3D符号相对路径', max_length=255)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'tt_select_dict',
                'db_table_comment': '标注符号数据表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtServiceData',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('service_ename', models.CharField(db_comment='服务英文名', max_length=100)),
                ('service_cname', models.CharField(db_comment='服务中文名', max_length=100)),
                ('service_aliasname', models.CharField(db_comment='服务别名', max_length=100)),
                ('service_url', models.CharField(db_comment='服务url', max_length=255)),
                ('map_names', models.CharField(db_comment='地图信息', max_length=2550)),
                ('service_interface_type', models.CharField(db_comment='服务接口类型', max_length=100)),
                ('service_component_type', models.CharField(db_comment='服务组件类型', max_length=100)),
                ('service_status', models.SmallIntegerField(blank=True, db_comment='服务状态', null=True)),
                ('remark', models.CharField(db_comment='备注信息', max_length=255)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'tt_service_data',
                'db_table_comment': '服务数据表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtServiceInterfaceType',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('interface_type_ename', models.CharField(db_comment='服务接口类型中文名', max_length=100)),
                ('interface_type_category', models.CharField(db_comment='服务接口所属大类', max_length=100)),
                ('remark', models.CharField(db_comment='备注信息', max_length=255)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'tt_service_interface_type',
                'db_table_comment': '服务接口类型表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtTaskSltFileData',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('file_size', models.DecimalField(blank=True, decimal_places=0, max_digits=24, null=True)),
                ('file_name', models.CharField(blank=True, max_length=255, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('file_suffix', models.CharField(blank=True, max_length=255, null=True)),
                ('path', models.CharField(blank=True, max_length=512, null=True)),
            ],
            options={
                'db_table': 'tt_task_slt_file_data',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtUploadFileData',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('file_size', models.DecimalField(blank=True, decimal_places=0, max_digits=24, null=True)),
                ('file_name', models.CharField(blank=True, max_length=255, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('file_suffix', models.CharField(blank=True, max_length=255, null=True)),
                ('path', models.CharField(blank=True, max_length=512, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_TT_UPLOAD_FILE_DATA',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtViewBookmarkData',
            fields=[
                ('id', models.BigAutoField(db_comment='id', primary_key=True, serialize=False)),
                ('lng', models.FloatField(blank=True, db_comment='经度', null=True)),
                ('lat', models.FloatField(blank=True, db_comment='纬度', null=True)),
                ('elv', models.FloatField(blank=True, db_comment='海拔', null=True)),
                ('heading', models.FloatField(blank=True, db_comment='视图方向', null=True)),
                ('pitch', models.FloatField(blank=True, db_comment='俯仰角', null=True)),
                ('roll', models.FloatField(blank=True, db_comment='视点高度', null=True)),
                ('view_bookmark_name', models.CharField(db_comment='备注信息', max_length=100)),
                ('bingtuan_code_list', models.CharField(db_comment='包含兵团编码信息', max_length=1000)),
                ('bingtuan_name_list', models.CharField(db_comment='包含兵团名称信息', max_length=1000)),
                ('remark', models.CharField(db_comment='备注信息', max_length=2550, null=True)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人', null=True)),
                ('modify_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('modify_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
            ],
            options={
                'db_table': 'tt_view_bookmark_data',
                'db_table_comment': '方向书签数据表',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='WjyyGzBggl',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('mc', models.CharField(blank=True, max_length=255, null=True)),
                ('baogao', models.JSONField(blank=True, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user', models.CharField(blank=True, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
                ('bg_type', models.BigIntegerField(blank=True, db_comment='1:', null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_BGGL',
                'db_table_comment': '报告管理',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='WjyyGzFxsj',
            fields=[
                ('id', models.BigIntegerField(db_comment='id', primary_key=True, serialize=False)),
                ('mc', models.CharField(blank=True, db_comment='名称', max_length=90, null=True)),
                ('sjfxxx', models.JSONField(blank=True, db_comment='事件分析信息', null=True)),
                ('create_time', models.DateTimeField(blank=True, db_comment='创建时间', null=True)),
                ('create_user', models.CharField(blank=True, db_comment='创建人', max_length=255, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建人id', null=True)),
                ('update_time', models.DateTimeField(blank=True, db_comment='更新时间', null=True)),
                ('update_user', models.CharField(blank=True, db_comment='更新人', null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, db_comment='更新人id', null=True)),
                ('dlhjfxfw', models.CharField(blank=True, db_comment='地理环境分析范围', max_length=255, null=True)),
                ('zhbzfxfw', models.CharField(blank=True, db_comment='综合保障分析范围', max_length=255, null=True)),
                ('zymbfxfw', models.CharField(blank=True, db_comment='重要目标分析范围', max_length=255, null=True)),
                ('jjdyfxfw', models.CharField(blank=True, db_comment='集结地域分析范围', max_length=255, null=True)),
                ('gcwzfxfw', models.CharField(blank=True, db_comment='观察位置分析范围', max_length=255, null=True)),
                ('wwfkfxfw', models.CharField(blank=True, db_comment='外围封控分析范围', max_length=255, null=True)),
                ('qsfxfxfw', models.CharField(blank=True, db_comment='驱散方向分析范围', max_length=255, null=True)),
                ('drawType', models.CharField(blank=True, db_comment='形状类型', max_length=255, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_FXSJ',
                'db_table_comment': '分析事件',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='WjyyGzSysModelWeight',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('title', models.CharField(blank=True, db_comment='模型名称_中文', max_length=255, null=True)),
                ('value', models.CharField(blank=True, db_comment='模型简称_英文', max_length=255, null=True)),
                ('create_time', models.DateTimeField(blank=True, null=True)),
                ('create_user', models.CharField(blank=True, max_length=255, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, null=True)),
                ('update_time', models.DateTimeField(blank=True, null=True)),
                ('update_user', models.CharField(blank=True, max_length=255, null=True)),
                ('update_user_id', models.BigIntegerField(blank=True, null=True)),
                ('model_type', models.BigIntegerField(blank=True, db_comment='模型类型：0：集结地，1：驱散方向', null=True)),
                ('template_id', models.BigIntegerField(blank=True, db_comment='模板id', null=True)),
                ('filter', models.CharField(blank=True, db_comment='权重条件', max_length=255, null=True)),
            ],
            options={
                'db_table': 'WJYY_GZ_SYS_MODEL_WEIGHT',
                'db_table_comment': '后台管理_模型权重管理',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='TtDroneAlerts',
            fields=[
                ('alert_id', models.AutoField(db_comment='告警记录唯一标识', primary_key=True, serialize=False)),
                ('alert_time', models.DateTimeField(db_comment='告警发生时间（带时区）', db_index=True)),
                ('alert_type_id', models.IntegerField(choices=[(30, '松材线虫病'), (31, '暴露垃圾'), (32, '渣土乱倒'), (33, '水域污染源'), (34, '工地未苫盖'), (35, '火灾隐患'), (36, '违法建设'), (37, '环境破坏'), (99, '其他')], db_comment='告警类型', db_index=True)),
                ('alert_level', models.SmallIntegerField(choices=[(1, '信息'), (2, '低级'), (3, '中级'), (4, '高级'), (5, '严重')], db_comment='告警级别（1-5级，1为最低，5为最高）', db_index=True)),
                ('alert_location', models.CharField(blank=True, db_comment='告警发生位置', max_length=255, null=True)),
                ('notes', models.CharField(blank=True, db_comment='告警发生描述', max_length=255, null=True)),
                ('points', models.CharField(blank=True, db_comment='警告点', max_length=255, null=True)),
                ('areas', models.CharField(blank=True, db_comment='警告面', max_length=2000, null=True)),
                ('status', models.IntegerField(choices=[(0, '新发现'), (1, '反馈处理'), (2, '处理完成'), (3, '已忽略'), (4, '误报')], db_comment='处理状态', db_index=True, default=0)),
                ('flight_id', models.BigIntegerField(blank=True, db_comment='飞行记录id', null=True)),
                ('drone_type', models.CharField(blank=True, db_comment='无人机类型', max_length=50, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建用户ID', null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('update_user_id', models.BigIntegerField(blank=True, db_comment='更新用户ID', null=True)),
                ('update_time', models.DateTimeField(auto_now=True, db_comment='更新时间')),
            ],
            options={
                'db_table': 'tt_drone_alerts',
                'db_table_comment': '告警记录',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='TtDroneBasicInfo',
            fields=[
                ('drone_id', models.BigAutoField(db_comment='无人机ID', primary_key=True, serialize=False)),
                ('drone_serial', models.CharField(db_comment='无人机序列号，设备出厂唯一编号', db_index=True, max_length=50, unique=True)),
                ('brand', models.CharField(choices=[('dji', '大疆'), ('autel', '道通'), ('parrot', 'Parrot'), ('yuneec', '昊翔'), ('other', '其他')], db_comment='无人机品牌', max_length=20)),
                ('model', models.CharField(db_comment='无人机型号（如 Mavic 3、Phantom 4）', max_length=50)),
                ('weight_kg', models.DecimalField(blank=True, db_comment='无人机重量（千克）', decimal_places=2, max_digits=5, null=True)),
                ('dimensions', models.CharField(blank=True, db_comment='无人机尺寸（展开/折叠状态）', max_length=100, null=True)),
                ('max_flight_time_min', models.IntegerField(blank=True, db_comment='最大续航时间（分钟）', null=True)),
                ('camera_model', models.CharField(blank=True, db_comment='搭载的相机型号', max_length=100, null=True)),
                ('camera_resolution', models.CharField(blank=True, db_comment='相机分辨率（如 4K/60fps）', max_length=50, null=True)),
                ('gps_precision', models.CharField(blank=True, db_comment='GPS定位精度（如厘米级/米级）', max_length=50, null=True)),
                ('obstacle_avoidance', models.BooleanField(db_comment='是否支持避障功能', default=False)),
                ('battery_capacity_mah', models.IntegerField(blank=True, db_comment='电池容量（毫安时）', null=True)),
                ('status', models.CharField(choices=[('online', '在线'), ('working', '工作中'), ('offline', '离线'), ('abnormal', '异常'), ('maintenance', '维护')], db_comment='设备状态', db_index=True, default='offline', max_length=20)),
                ('firmware_version', models.CharField(blank=True, db_comment='当前固件版本号', max_length=30, null=True)),
                ('department', models.CharField(blank=True, db_comment='所属部门/团队', db_index=True, max_length=50, null=True)),
                ('responsible_person', models.CharField(blank=True, db_comment='设备负责人', max_length=50, null=True)),
                ('purchase_date', models.DateField(blank=True, db_comment='采购日期', null=True)),
                ('purchase_price', models.DecimalField(blank=True, db_comment='采购价格（元）', decimal_places=2, max_digits=10, null=True)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建用户ID', null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('update_user_id', models.BigIntegerField(blank=True, db_comment='更新用户ID', null=True)),
                ('update_time', models.DateTimeField(auto_now=True, db_comment='更新时间')),
            ],
            options={
                'db_table': 'tt_drone_basic_info',
                'db_table_comment': '无人机设备基础信息表',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='TtDroneTasks',
            fields=[
                ('id', models.BigAutoField(db_comment='任务ID', primary_key=True, serialize=False)),
                ('task_name', models.CharField(db_comment='任务的唯一名称，用于标识和检索任务', db_index=True, max_length=255, unique=True)),
                ('task_status', models.CharField(choices=[('pending_review', '待审核'), ('pending', '待执行'), ('running', '执行中'), ('completed', '已完成'), ('cancelled', '已取消'), ('failed', '执行失败')], db_comment='任务状态', db_index=True, default='pending_review', max_length=20)),
                ('task_purpose', models.CharField(choices=[('photography', '拍摄'), ('panoramic', '全景'), ('3d', '三维'), ('orthophoto', '正射'), ('video', '视频')], db_comment='任务的主要用途类型', max_length=20)),
                ('task_urgency', models.CharField(choices=[('low', '低'), ('medium', '中'), ('high', '高'), ('urgent', '紧急')], db_comment='任务的紧急程度级别', default='medium', max_length=10)),
                ('task_shape', models.CharField(choices=[('Point', '点'), ('Line', '线'), ('Circle', '圆形'), ('Polygon', '多边形'), ('Rectangle', '正北矩形')], db_comment='任务区域的几何形状类型', default='Polygon', max_length=10)),
                ('center_coordinates', models.CharField(blank=True, db_comment='任务区域中心坐标，格式：经度,纬度', max_length=50, null=True)),
                ('perimeter', models.DecimalField(db_comment='任务区域的周长，单位：米', decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('area', models.DecimalField(db_comment='任务区域的面积，单位：平方千米', decimal_places=4, default=Decimal('0.0000'), max_digits=10)),
                ('task_shape_points', models.TextField(db_comment='区域几何数据，GeoJSON格式字符串', default='{}')),
                ('location_detail', models.TextField(db_comment='任务位置的详细描述信息', default='')),
                ('thumbnail', models.CharField(db_comment='任务缩略图 image url', default='', max_length=500)),
                ('mark_count', models.BigIntegerField(blank=True, db_comment='标记数量', default=0, null=True)),
                ('photo_count', models.BigIntegerField(blank=True, db_comment='照片数量', default=0, null=True)),
                ('remark', models.TextField(blank=True, db_comment='任务备注信息', null=True)),
                ('shenh', models.BigIntegerField(db_comment='审核 0待审核 1通过 2未通过', default=0)),
                ('create_user_id', models.BigIntegerField(blank=True, db_comment='创建用户ID', null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('update_user_id', models.BigIntegerField(blank=True, db_comment='更新用户ID', null=True)),
                ('update_time', models.DateTimeField(auto_now=True, db_comment='更新时间')),
                ('selected_drone', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_tasks', to='my_app.ttdronebasicinfo')),
            ],
            options={
                'db_table': 'tt_drone_tasks',
                'db_table_comment': '任务表',
                'managed': True,
            },
        ),
        migrations.AddIndex(
            model_name='ttdronebasicinfo',
            index=models.Index(fields=['status'], name='idx_drone_status'),
        ),
        migrations.AddIndex(
            model_name='ttdronebasicinfo',
            index=models.Index(fields=['department', 'status'], name='idx_drone_dept_status'),
        ),
        migrations.AddIndex(
            model_name='ttdronebasicinfo',
            index=models.Index(fields=['brand', 'model'], name='idx_drone_brand_model'),
        ),
        migrations.AddIndex(
            model_name='ttdronebasicinfo',
            index=models.Index(fields=['create_time'], name='idx_drone_create_time'),
        ),
        migrations.AddField(
            model_name='ttdronealerts',
            name='drone',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='drone_alerts', to='my_app.ttdronebasicinfo'),
        ),
        migrations.AddField(
            model_name='ttdronealerts',
            name='task',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='my_app.ttdronetasks'),
        ),
        migrations.AddIndex(
            model_name='ttdronetasks',
            index=models.Index(fields=['task_status'], name='idx_task_status'),
        ),
        migrations.AddIndex(
            model_name='ttdronetasks',
            index=models.Index(fields=['task_purpose'], name='idx_task_purpose'),
        ),
        migrations.AddIndex(
            model_name='ttdronetasks',
            index=models.Index(fields=['selected_drone', 'task_status'], name='idx_drone_task_status'),
        ),
        migrations.AddIndex(
            model_name='ttdronetasks',
            index=models.Index(fields=['task_urgency', 'task_status'], name='idx_urgency_status'),
        ),
        migrations.AddIndex(
            model_name='ttdronetasks',
            index=models.Index(fields=['create_time'], name='idx_task_create_time'),
        ),
        migrations.AddIndex(
            model_name='ttdronealerts',
            index=models.Index(fields=['alert_time'], name='idx_alert_time'),
        ),
        migrations.AddIndex(
            model_name='ttdronealerts',
            index=models.Index(fields=['alert_time', 'status'], name='idx_alert_time_status'),
        ),
        migrations.AddIndex(
            model_name='ttdronealerts',
            index=models.Index(fields=['alert_type_id'], name='idx_alert_type'),
        ),
        migrations.AddIndex(
            model_name='ttdronealerts',
            index=models.Index(fields=['alert_level'], name='idx_alert_level'),
        ),
        migrations.AddIndex(
            model_name='ttdronealerts',
            index=models.Index(fields=['alert_type_id', 'alert_level'], name='idx_alert_type_level'),
        ),
        migrations.AddIndex(
            model_name='ttdronealerts',
            index=models.Index(fields=['status'], name='idx_alert_status'),
        ),
        migrations.AddIndex(
            model_name='ttdronealerts',
            index=models.Index(fields=['task', 'alert_type_id'], name='idx_task_alert_type'),
        ),
        migrations.AddIndex(
            model_name='ttdronealerts',
            index=models.Index(fields=['create_time'], name='idx_alert_create_time'),
        ),
    ]
