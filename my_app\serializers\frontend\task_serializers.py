#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time : 2024-12-26 当前系统时间
# <AUTHOR> zhous
# @Email : <EMAIL>
# @File : task_serializers.py
# @Descr : 前端任务序列化器
# @Software: VSCode

from rest_framework import serializers
from my_app.models import TtDroneTasks, TtDroneBasicInfo

class TaskCreateSerializer(serializers.ModelSerializer):
    """任务创建序列化器 - 前端专用"""

    class Meta:
        model = TtDroneTasks
        exclude = [
            'id', 'created_by_id', 'updated_by_id', 'reviewer_id',
            'create_time', 'update_time', 'review_time', 'review_notes',
            'analysis_start_time', 'analysis_end_time', 'ai_analysis_result',
            'review_status','mark_count', 'photo_count', 'thumbnail'
        ]

    def validate_task_name(self, value):
        """验证任务名称"""
        if len(value.strip()) < 2:
            raise serializers.ValidationError("任务名称至少需要2个字符")
        return value.strip()

class TaskReadOnlySerializer(serializers.ModelSerializer):
    """任务只读序列化器 - 前端专用"""

    class Meta:
        model = TtDroneTasks
        fields = '__all__'
