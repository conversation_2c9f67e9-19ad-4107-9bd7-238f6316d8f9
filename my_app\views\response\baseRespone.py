import json
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Optional

from rest_framework.response import Response

from my_app.enum.localization_enum import SysInfoEnum


@dataclass
class ResultCodeMsgEnum(Enum):
    """
    定义结果代码和消息的枚举类，包含不同操作结果对应的代码和消息，支持中英文两种语言。
    """
    # 请求成功，中文消息
    REQUEST_SUCCESS = {"code": 200, "msg": SysInfoEnum.REQUEST_SUCCESS_CH}
    # 请求成功，英文消息
    REQUEST_SUCCESS_EN = {"code": 200, "msg": SysInfoEnum.REQUEST_SUCCESS_EN}

    # 创建操作失败，中文消息
    CREATE_ERROR = {'code': 1000, 'msg': SysInfoEnum.ADD_FAIL_CH}
    # 创建操作失败，英文消息
    CREATE_ERROR_EN = {'code': 1000, 'msg': SysInfoEnum.ADD_FAIL_EN}

    # 操作错误，中文消息
    OPERATION_ERROR = {'code': 1001, 'msg': SysInfoEnum.OPERATE_FAIL_CH}
    # 操作错误，英文消息
    OPERATION_ERROR_EN = {'code': 1001, 'msg': SysInfoEnum.OPERATE_FAIL_EN}

    # ID 是必需的，中文消息
    ID_IS_A_MUST = {'code': 1002, 'msg': SysInfoEnum.ID_MUST_CH}
    # ID 是必需的，英文消息
    ID_IS_A_MUST_EN = {'code': 1002, 'msg': SysInfoEnum.ID_MUST_EN}
    # 可根据需要添加更多枚举值


@dataclass
class PageResult(dict):
    """
    分页结果数据类，继承自字典，用于存储分页相关信息。
    """
    # 总记录数，可选参数
    count: Optional[int] = None
    # 下一页的链接，可选参数
    next: Optional[str] = None
    # 上一页的链接，可选参数
    previous: Optional[str] = None
    # 结果数据，可选参数
    results: Optional[any] = None

    def set_count_result(self, count, results):
        """
        设置分页结果的总记录数和结果数据。

        :param count: 总记录数
        :param results: 结果数据
        """
        self.count = count
        self.results = results


@dataclass
class Result(dict):
    """
    通用结果数据类，继承自字典，用于封装接口返回的结果信息。
    """
    # 操作是否成功，默认为 True
    success: bool = True
    # 结果代码，可选参数
    code: Optional[int] = None
    # 结果消息，可选参数
    msg: Optional[str] = None
    # 请求时间，默认为当前时间，格式为 'YYYY-MM-DD HH:MM:SS'
    date: datetime = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    # 请求 ID，可选参数
    request_id: Optional[str] = None
    # 结果对象，可选参数
    obj: Optional[any] = None

    @staticmethod
    def ok():
        """
        返回一个表示请求成功的响应。

        :return: 包含成功结果信息的响应对象
        """
        return Response(Result().code_msg(ResultCodeMsgEnum.REQUEST_SUCCESS).__dict__)

    @staticmethod
    def cres_ures(cres, id_name=None):
        """
        处理创建操作的结果，根据返回数据中的 ID 判断操作是否成功。

        :param cres: 创建操作的响应对象
        :param id_name: ID 字段的名称，可选参数，默认为 'id'
        :return: 包含操作结果信息的响应对象
        """
        data = cres.data
        if id_name:
            id = data[id_name]
        else:
            id = data['id']
        json_data = json.dumps(data)
        if id is not None:
            # 新增成功
            return Response(Result().code_msg(ResultCodeMsgEnum.REQUEST_SUCCESS).set_obj(id).__dict__)
        else:
            return Result.fail(ResultCodeMsgEnum.CREATE_ERROR.value['msg'], json_data)

    @staticmethod
    def cres_ures_data(data):
        """
        处理创建操作的结果数据，根据数据中的 ID 判断操作是否成功。

        :param data: 创建操作的结果数据
        :return: 包含操作结果信息的响应对象
        """
        id = data.id
        if id is not None:
            # 新增成功
            return Response(Result().code_msg(ResultCodeMsgEnum.REQUEST_SUCCESS).set_obj(id).__dict__)
        else:
            json_data = json.dumps(data)
            return Result.fail(ResultCodeMsgEnum.CREATE_ERROR.value['msg'], json_data)

    @staticmethod
    def cres_ures_update(count):
        """
        处理更新操作的结果，根据更新的记录数判断操作是否成功。

        :param count: 更新的记录数
        :return: 包含操作结果信息的响应对象
        """
        if count > 0:
            return Result().ok()
        else:
            msg = "更新失败"
            return Result.fail(msg, msg)

    @staticmethod
    def create_enum(name, values):
        """
        动态创建枚举类。

        :param name: 枚举类的名称
        :param values: 枚举值的字典
        :return: 新创建的枚举类
        """
        return Enum(name, values)

    @staticmethod
    def list(obj, **kwargs):
        """
        处理列表数据的返回结果，可自定义消息。

        :param obj: 列表数据
        :param kwargs: 可选参数，可包含 'message' 用于自定义消息
        :return: 包含列表数据和结果信息的字典
        """
        message = ResultCodeMsgEnum.REQUEST_SUCCESS
        if "message" in kwargs:
            MyEnum = Result.create_enum('MyEnum', {'LIST_SUCCESS': {"code": 200, "msg": kwargs["message"]}})
            message = MyEnum.LIST_SUCCESS
        return Result().code_msg(message).set_obj(obj).__dict__

    @staticmethod
    def page_list(obj=[], page_count=0, **kwargs):
        """
        处理分页列表数据的返回结果，支持中英文消息切换。

        :param obj: 分页列表数据，默认为空列表
        :param page_count: 总记录数，默认为 0
        :param kwargs: 可选参数，可包含 'localization' 用于指定语言（'EN' 表示英文）
        :return: 包含分页列表数据和结果信息的字典
        """
        if obj == 0:
            obj = []
        pageResult = PageResult()
        pageResult.set_count_result(page_count, obj)
        Req_sucess = ResultCodeMsgEnum.REQUEST_SUCCESS
        if "localization" in kwargs:
            local = kwargs["localization"]
            if local == "EN":
                Req_sucess = ResultCodeMsgEnum.REQUEST_SUCCESS_EN
        return Result().code_msg(Req_sucess).set_obj(pageResult.__dict__).__dict__

    @staticmethod
    def list_response(response):
        """
        处理列表响应，将响应数据封装为结果对象并返回响应。

        :param response: 列表响应对象
        :return: 包含列表数据和结果信息的响应对象
        """
        data = response.data
        res = Result.list(data)
        return Response(res)

    @staticmethod
    def fail(msg, obj, **kwargs):
        """
        返回一个表示操作失败的响应，支持中英文消息切换。

        :param msg: 失败消息
        :param obj: 失败结果对象
        :param kwargs: 可选参数，可包含 'localization' 用于指定语言（'EN' 表示英文）
        :return: 包含失败结果信息的响应对象
        """
        if obj is None:
            obj = ""
        if isinstance(obj, str):
            obj = json.dumps(obj)

        Ope_fail = ResultCodeMsgEnum.OPERATION_ERROR
        if "localization" in kwargs:
            local = kwargs["localization"]
            if local == "EN":
                Ope_fail = ResultCodeMsgEnum.OPERATION_ERROR_EN
        return Response(Result()
                        .set_success(False)
                        .set_msg(msg)
                        .set_obj(json.loads(obj))
                        .set_code(Ope_fail.value['code'])
                        .__dict__)

    @staticmethod
    def sucess(msg, obj=None):
        """
        返回一个表示操作成功的响应。

        :param msg: 成功消息
        :param obj: 成功结果对象，可选参数
        :return: 包含成功结果信息的响应对象
        """
        return Response(Result()
                        .set_success(True)
                        .set_msg(msg)
                        .set_obj(obj)
                        .set_code(ResultCodeMsgEnum.REQUEST_SUCCESS.value['code'])
                        .__dict__)

    @staticmethod
    def sucess_obj(obj):
        """
        返回一个表示操作成功的响应，使用默认的成功消息。

        :param obj: 成功结果对象
        :return: 包含成功结果信息的响应对象
        """
        return Response(Result()
                        .set_success(True)
                        .set_msg(ResultCodeMsgEnum.REQUEST_SUCCESS.value['msg'])
                        .set_obj(obj)
                        .set_code(ResultCodeMsgEnum.REQUEST_SUCCESS.value['code'])
                        .__dict__)

    @staticmethod
    def fail_no_response(msg, obj):
        """
        返回一个表示操作失败的结果字典，不包含响应对象。

        :param msg: 失败消息
        :param obj: 失败结果对象
        :return: 包含失败结果信息的字典
        """
        if isinstance(obj, str):
            obj = json.dumps(obj)
        return Result().set_success(False).set_msg(msg).set_obj(json.loads(obj)).set_code(
            ResultCodeMsgEnum.OPERATION_ERROR.value['code']).__dict__

    @staticmethod
    def fail_response(resultCodeMsgEnum, obj):
        """
        返回一个表示操作失败的响应，使用指定的结果代码和消息。

        :param resultCodeMsgEnum: 结果代码和消息的枚举值
        :param obj: 失败结果对象
        :return: 包含失败结果信息的响应对象
        """
        return Response(Result().set_success(False).set_code_msg(resultCodeMsgEnum).set_obj(json.loads(obj)).__dict__)

    def set_code_msg(self, resultCodeMsgEnum):
        """
        设置结果代码和消息。

        :param resultCodeMsgEnum: 结果代码和消息的枚举值
        :return: 当前结果对象
        """
        self.set_msg(resultCodeMsgEnum.value['msg'])
        self.set_code(resultCodeMsgEnum.value['code'])
        return self

    @staticmethod
    def fail_dick(resultCodeMsgEnum, obj):
        """
        返回一个表示操作失败的结果字典，使用指定的结果代码和消息。

        :param resultCodeMsgEnum: 结果代码和消息的枚举值
        :param obj: 失败结果对象
        :return: 包含失败结果信息的字典
        """
        return Result().set_success(False).set_msg(resultCodeMsgEnum.value['msg']).set_code(
            resultCodeMsgEnum.value['code']).set_obj(obj)

    def api_name(self, api_name):
        """
        设置 API 名称。

        :param api_name: API 名称
        :return: 当前结果对象
        """
        self.api_name = api_name
        return self

    def set_obj(self, obj):
        """
        设置结果对象。

        :param obj: 结果对象
        :return: 当前结果对象
        """
        self.obj = obj
        return self

    def code_msg(self, result_code):
        """
        设置结果代码和消息。

        :param result_code: 结果代码和消息的枚举值
        :return: 当前结果对象
        """
        self.msg = result_code.value['msg']
        self.code = result_code.value['code']
        return self

    def set_code(self, code):
        """
        设置结果代码。

        :param code: 结果代码
        :return: 当前结果对象
        """
        self.code = code
        return self

    def set_msg(self, msg):
        """
        设置结果消息。

        :param msg: 结果消息
        :return: 当前结果对象
        """
        self.msg = msg
        return self

    def set_obj(self, obj):
        """
        设置结果对象。

        :param obj: 结果对象
        :return: 当前结果对象
        """
        self.obj = obj
        return self

    def set_success(self, success):
        """
        设置操作是否成功。

        :param success: 操作是否成功的布尔值
        :return: 当前结果对象
        """
        self.success = success
        return self