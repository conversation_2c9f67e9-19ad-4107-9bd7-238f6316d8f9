{"asset": {"version": "2.0", "generator": "babylon.js glTF exporter for 3ds max 2018 v1.3.33"}, "scene": 0, "scenes": [{"nodes": [0, 1, 2, 3, 4, 5], "extensions": {}}], "nodes": [{"mesh": 0, "translation": [0.05655375, 1.00156593, -2.61934474e-10], "rotation": [0.0, -0.8080684, 0.0, 0.589088738], "scale": [0.8000001, 0.799999952, 0.8000001], "name": "SA20_01_oth2_43"}, {"mesh": 1, "translation": [1.6409446, 0.372082, 0.903918743], "rotation": [-1.306072e-06, 2.35957216e-07, -0.177783713, 0.984069645], "scale": [0.99999994, 0.99999994, 1.0], "name": "s004"}, {"mesh": 2, "translation": [-1.393246, 0.372082, 0.903918743], "rotation": [-1.306072e-06, 2.35957216e-07, -0.177783713, 0.984069645], "scale": [0.99999994, 0.99999994, 1.0], "name": "s001"}, {"mesh": 3, "translation": [1.6409446, 0.372082, -0.864311934], "rotation": [-1.306072e-06, 2.35957216e-07, -0.177783713, 0.984069645], "scale": [0.99999994, 0.99999994, 1.0], "name": "s002"}, {"mesh": 4, "translation": [-1.393246, 0.372082, -0.864311934], "rotation": [-1.306072e-06, 2.35957216e-07, -0.177783713, 0.984069645], "scale": [0.99999994, 0.99999994, 1.0], "name": "s003"}, {"translation": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "name": "Default light"}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "COLOR_0": 3, "TEXCOORD_0": 4}, "indices": 0, "mode": 4, "material": 0}, {"attributes": {"POSITION": 6, "NORMAL": 7, "COLOR_0": 8, "TEXCOORD_0": 9}, "indices": 5, "mode": 4, "material": 1}], "name": "SA20_01_oth2_43"}, {"primitives": [{"attributes": {"POSITION": 11, "NORMAL": 12, "COLOR_0": 13, "TEXCOORD_0": 14}, "indices": 10, "mode": 4, "material": 0}], "name": "s004"}, {"primitives": [{"attributes": {"POSITION": 16, "NORMAL": 17, "COLOR_0": 18, "TEXCOORD_0": 19}, "indices": 15, "mode": 4, "material": 0}], "name": "s001"}, {"primitives": [{"attributes": {"POSITION": 21, "NORMAL": 22, "COLOR_0": 23, "TEXCOORD_0": 24}, "indices": 20, "mode": 4, "material": 0}], "name": "s002"}, {"primitives": [{"attributes": {"POSITION": 26, "NORMAL": 27, "COLOR_0": 28, "TEXCOORD_0": 29}, "indices": 25, "mode": 4, "material": 0}], "name": "s003"}], "accessors": [{"bufferView": 0, "componentType": 5123, "count": 2874, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "componentType": 5126, "count": 2874, "max": [1.92245185, 0.801172137, 3.38942575], "min": [-1.96246231, -0.981652, -3.33553982], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 34488, "componentType": 5126, "count": 2874, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "componentType": 5126, "count": 2874, "type": "VEC4", "name": "accessorColors"}, {"bufferView": 3, "componentType": 5126, "count": 2874, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 5748, "componentType": 5123, "count": 1842, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 68976, "componentType": 5126, "count": 1842, "max": [1.88677514, 0.981652141, 3.337401], "min": [-1.93430769, -0.981652, -3.15899372], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 91080, "componentType": 5126, "count": 1842, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 45984, "componentType": 5126, "count": 1842, "type": "VEC4", "name": "accessorColors"}, {"bufferView": 3, "byteOffset": 22992, "componentType": 5126, "count": 1842, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 9432, "componentType": 5123, "count": 120, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 113184, "componentType": 5126, "count": 120, "max": [0.3774947, 0.3624816, 0.138292253], "min": [-0.3712386, -0.382288218, -0.138269782], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 114624, "componentType": 5126, "count": 120, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 75456, "componentType": 5126, "count": 120, "type": "VEC4", "name": "accessorColors"}, {"bufferView": 3, "byteOffset": 37728, "componentType": 5126, "count": 120, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 9672, "componentType": 5123, "count": 120, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 116064, "componentType": 5126, "count": 120, "max": [0.3774947, 0.3624816, 0.138292313], "min": [-0.3712386, -0.382288218, -0.138269782], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 117504, "componentType": 5126, "count": 120, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 77376, "componentType": 5126, "count": 120, "type": "VEC4", "name": "accessorColors"}, {"bufferView": 3, "byteOffset": 38688, "componentType": 5126, "count": 120, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 9912, "componentType": 5123, "count": 120, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 118944, "componentType": 5126, "count": 120, "max": [0.3774947, 0.3624816, 0.1382922], "min": [-0.3712386, -0.382288218, -0.138269722], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 120384, "componentType": 5126, "count": 120, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 79296, "componentType": 5126, "count": 120, "type": "VEC4", "name": "accessorColors"}, {"bufferView": 3, "byteOffset": 39648, "componentType": 5126, "count": 120, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 10152, "componentType": 5123, "count": 120, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 121824, "componentType": 5126, "count": 120, "max": [0.3774947, 0.3624816, 0.1382922], "min": [-0.3712386, -0.382288247, -0.138269663], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 123264, "componentType": 5126, "count": 120, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 81216, "componentType": 5126, "count": 120, "type": "VEC4", "name": "accessorColors"}, {"bufferView": 3, "byteOffset": 40608, "componentType": 5126, "count": 120, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 4, "componentType": 5126, "count": 35, "max": [1.13333333], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "componentType": 5126, "count": 35, "type": "VEC4", "name": "accessorAnimationRotations"}, {"bufferView": 4, "byteOffset": 140, "componentType": 5126, "count": 35, "max": [1.13333333], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "byteOffset": 560, "componentType": 5126, "count": 35, "type": "VEC4", "name": "accessorAnimationRotations"}, {"bufferView": 4, "byteOffset": 280, "componentType": 5126, "count": 35, "max": [1.13333333], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "byteOffset": 1120, "componentType": 5126, "count": 35, "type": "VEC4", "name": "accessorAnimationRotations"}, {"bufferView": 4, "byteOffset": 420, "componentType": 5126, "count": 35, "max": [1.13333333], "min": [0.0], "type": "SCALAR", "name": "accessorAnimationInput"}, {"bufferView": 5, "byteOffset": 1680, "componentType": 5126, "count": 35, "type": "VEC4", "name": "accessorAnimationRotations"}], "bufferViews": [{"buffer": 0, "byteLength": 10392, "name": "bufferViewScalar"}, {"buffer": 0, "byteOffset": 10392, "byteLength": 124704, "byteStride": 12, "name": "bufferViewFloatVec3"}, {"buffer": 0, "byteOffset": 135096, "byteLength": 83136, "byteStride": 16, "name": "bufferViewFloatVec4"}, {"buffer": 0, "byteOffset": 218232, "byteLength": 41568, "byteStride": 8, "name": "bufferViewFloatVec2"}, {"buffer": 0, "byteOffset": 259800, "byteLength": 560, "name": "bufferViewAnimationFloatScalar"}, {"buffer": 0, "byteOffset": 260360, "byteLength": 2240, "name": "bufferViewAnimationFloatVec4"}], "buffers": [{"uri": "jingche_move.bin", "byteLength": 262600}], "materials": [{"pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "baseColorTexture": {"index": 0, "texCoord": 0}, "metallicFactor": 0.0, "roughnessFactor": 0.9}, "emissiveFactor": [0.0, 0.0, 0.0], "alphaMode": "OPAQUE", "name": "Material #27"}, {"pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "baseColorTexture": {"index": 1, "texCoord": 0}, "metallicFactor": 0.0, "roughnessFactor": 0.9}, "emissiveFactor": [0.0, 0.0, 0.0], "alphaMode": "OPAQUE", "name": "Material #72"}], "textures": [{"sampler": 0, "source": 0, "name": "car_saturn2.jpg"}, {"sampler": 0, "source": 1, "name": "car_saturn3.jpg"}], "images": [{"uri": "car_saturn2.jpg"}, {"uri": "car_saturn3.jpg"}], "samplers": [{"magFilter": 9729, "minFilter": 9987, "wrapS": 10497, "wrapT": 10497}], "animations": [{"channels": [{"sampler": 0, "target": {"node": 1, "path": "rotation"}}, {"sampler": 1, "target": {"node": 2, "path": "rotation"}}, {"sampler": 2, "target": {"node": 3, "path": "rotation"}}, {"sampler": 3, "target": {"node": 4, "path": "rotation"}}], "samplers": [{"input": 30, "interpolation": "LINEAR", "output": 31}, {"input": 32, "interpolation": "LINEAR", "output": 33}, {"input": 34, "interpolation": "LINEAR", "output": 35}, {"input": 36, "interpolation": "LINEAR", "output": 37}], "name": "All Animations"}]}