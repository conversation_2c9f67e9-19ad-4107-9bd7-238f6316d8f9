#!/usr/bin/python3.10.16
# -*- coding: utf-8 -*-
# @Time : 2024-12-26 当前系统时间
# <AUTHOR> zhous
# @Email : <EMAIL>
# @File : alert_serializers.py
# @Descr : 前端预警序列化器
# @Software: VSCode

from rest_framework import serializers
from my_app.models import TtDroneAlerts

class AlertReadOnlySerializer(serializers.ModelSerializer):
    """预警只读序列化器 - 前端专用"""

    class Meta:
        model = TtDroneAlerts
        fields = '__all__'
        
    def get_task_info(self, obj):
        """获取关联任务信息"""
        if obj.task:
            return {
                'id': obj.task.id,
                'task_name': obj.task.task_name,
                'task_status': obj.task.task_status
            }
        return None
        
    def get_drone_info(self, obj):
        """获取关联无人机信息"""
        if obj.drone:
            return {
                'id': obj.drone.id,
                'drone_serial': obj.drone.drone_serial,
                'brand': obj.drone.brand,
                'model': obj.drone.model
            }
        return None
