import logging
import platform

from django.db.models import Q

from my_app.models import TtViewBookmarkData, TtFeatureAnnotationData

# 获取名为 'django' 的日志记录器，用于记录日志信息
logger = logging.getLogger('django')
# 获取当前操作系统的名称，并将其转换为小写形式，存储在 platformType 变量中
platformType = platform.system().lower()


class businessHelper:
    # 类的初始化方法，目前该方法为空，可能是拼写错误，通常应为 __init__
    def __int__(self):
        pass

    @staticmethod
    def build_update_field_sql(field_ename, field_type, field_value):
        """
        根据提供的字段英文名称、字段类型和字段值构建SQL更新语句的一部分。

        :param field_ename: 字段的英文名称，用于指定要更新的字段
        :param field_type: 字段的类型，取值可以是 "string"、"datetime"、"date"、"int"、"float"、"bool" 等
        :param field_value: 字段要更新的值
        :return: 构建好的SQL更新语句片段，用于更新指定字段的值
        """
        update_sql = ""
        # 判断字段类型是否为字符串、日期时间或日期类型
        if field_type.lower() == "string" or field_type.lower() == "datetime" or field_type.lower() == "date":
            # 对于字符串、日期时间和日期类型，需要用单引号包裹字段值
            update_sql = " {} = '{}'".format(field_ename, field_value)
        # 判断字段类型是否为整数或浮点数类型
        if field_type.lower() == "int" or field_type.lower() == "float":
            # 整数和浮点数类型直接使用字段值，无需用引号包裹
            update_sql = " {} = {}".format(field_ename, field_value)
        # 判断字段类型是否为布尔类型
        if field_type.lower() == "bool":
            # 如果字段值为True，将其转换为1
            if field_value:
                field_value = 1
            # 如果字段值为False，将其转换为0
            elif not field_value:
                field_value = 0
            update_sql = " {} = {}".format(field_ename, field_value)
        return update_sql

    @staticmethod
    def convert_to_dm_field_type(custom_field_type):
        """
        将自定义的字段类型转换为数据库管理系统（DM）所支持的字段类型。

        :param custom_field_type: 自定义的字段类型，如 "string"、"int"、"float" 等
        :return: 转换后的数据库管理系统支持的字段类型
        """
        if custom_field_type == "string":
            dm_field = "VARCHAR(25500)"
        elif custom_field_type == "int":
            dm_field = "INTEGER"
        elif custom_field_type == "float":
            dm_field = "FLOAT"
        elif custom_field_type == "datetime":
            dm_field = "DATETIME"
        # 这里重复判断了 "datetime"，推测是多余的，可能应改为 "date"
        elif custom_field_type == "date":
            dm_field = "DATE"
        elif custom_field_type == "bool":
            dm_field = "TINYINT"
        else:
            # 如果自定义字段类型不匹配上述任何类型，默认使用 "VARCHAR(255)"
            dm_field = "VARCHAR(255)"
        return dm_field

    @staticmethod
    def convert_to_custom_field_type(dm_field_type):
        """
        将数据库管理系统（DM）的字段类型转换为自定义的字段类型。

        :param dm_field_type: 数据库管理系统的字段类型，如 "INT"、"FLOAT"、"DATETIME" 等
        :return: 转换后的自定义字段类型
        """
        # 如果数据库字段类型包含 "INT"
        if "INT" in dm_field_type:
            custom_field_type = "string"
        # 如果数据库字段类型包含 "FLOAT" 或 "DOUBLE"
        elif "FLOAT" in dm_field_type or "DOUBLE" in dm_field_type:
            custom_field_type = "float"
        # 如果数据库字段类型包含 "DATETIME" 或 "TIMESTAMP"
        elif "DATETIME" in dm_field_type or "TIMESTAMP" in dm_field_type:
            custom_field_type = "datetime"
        # 如果数据库字段类型包含 "DATE" 且不包含 "DATETIME"
        elif "DATE" in dm_field_type and "DATETIME" not in dm_field_type:
            custom_field_type = "date"
        # 如果数据库字段类型包含 "BOOLEAN"
        elif "BOOLEAN" in dm_field_type:
            custom_field_type = "bool"
        else:
            # 如果数据库字段类型不匹配上述任何类型，默认使用 "string"
            custom_field_type = "string"
        return custom_field_type

    @staticmethod
    # 方向书签数据查询
    def get_view_bookmark_data_query_result(data, request):
        """
        根据传入的数据和请求对象，查询方向书签数据。

        :param data: 包含查询条件的字典，例如 {'bookmark_name': '示例书签名称'}
        :param request: Django的请求对象，用于获取当前用户的信息
        :return: 符合查询条件的方向书签数据查询结果集，按创建时间降序排列
        """
        # 创建一个查询条件对象，用于组合多个查询条件
        query_conditions = Q()

        # 从传入的数据字典中获取书签名称
        bookmark_name = data.get('bookmark_name')
        # 添加查询条件：书签的创建用户ID等于当前请求的用户ID
        query_conditions &= Q(**{'create_user_id': request.auth.user_id})
        # 如果书签名称不为空
        if bookmark_name is not None:
            # 添加查询条件：书签名称包含传入的书签名称（不区分大小写）
            query_conditions &= Q(view_bookmark_name__icontains=bookmark_name)

        # 根据组合的查询条件进行数据库查询，并按创建时间降序排列结果
        return TtViewBookmarkData.objects.filter(query_conditions).order_by("-create_time")

    @staticmethod
    # 标注数据查询
    def get_annotation_data_query_result(data, request):
        """
        根据传入的数据和请求对象，查询标注数据。

        :param data: 包含查询条件的字典，例如 {'name': '示例标注名称', 'type': '示例标注类型'}
        :param request: Django的请求对象，用于获取当前用户的信息
        :return: 符合查询条件的标注数据查询结果集，按创建时间降序排列
        """
        # 创建一个查询条件对象，用于组合多个查询条件
        query_conditions = Q()

        # 从传入的数据字典中获取标注名称
        name = data.get('name')
        # 从传入的数据字典中获取标注符号类型编号
        type = data.get('type')
        # 添加查询条件：标注的创建用户ID等于当前请求的用户ID
        query_conditions &= Q(**{'create_user_id': request.auth.user_id})
        # 如果标注名称不为空
        if name is not None:
            # 添加查询条件：标注名称包含传入的标注名称（不区分大小写）
            query_conditions &= Q(name__icontains=name)
        # 如果标注符号类型编号不为空
        if type is not None:
            # 添加查询条件：标注符号类型编号等于传入的类型编号
            query_conditions &= Q(**{'type': type})

        # 根据组合的查询条件进行数据库查询，并按创建时间降序排列结果
        return TtFeatureAnnotationData.objects.filter(query_conditions).order_by("-create_time")