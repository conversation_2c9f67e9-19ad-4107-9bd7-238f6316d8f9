# -*- coding: utf-8 -*-
"""
===================================
#!/usr/bin/python3.9
@Author: chenxw
@Email : <EMAIL>
@File: serializers.py.py
@Date: Create in 2021/2/5 19:49
@Description: 序列化器
@ Software: PyCharm
===================================
"""

from rest_framework import serializers

from my_app.models import AuthUser, TtUploadFileData, SysConfig, SysDepartment, SysLog, SysMenu, SysOss, SysRole, \
    SysRoleMenu, SysUser, \
    SysUserRole, SysUserToken, TmDdistrict, SysParam, TtAnnotationSysmbolData, TtAnnotationType, \
    TtFeatureAnnotationData, TtFriendFoeInfo, TtIntelligenceSource, TtPoiData, TtViewBookmarkData, TtPointType, \
    TmBingtuan, TtFieldType, TtFeatureAnnotationeNewFields, TtServiceData, TtServiceInterfaceType, TtKeyPointAreaData, \
    TtDroneTasks, TtDroneBasicInfo, TtDroneAlerts, TtFlightSchedule


# 用户登录请求序列化器
class LoginRequestSerializer(serializers.Serializer):
    """
    用户登录请求的序列化器，只包含登录所需的字段
    """
    username = serializers.CharField(
        max_length=150,
        help_text="用户名",
        required=True
    )
    password = serializers.CharField(
        max_length=128,
        help_text="密码",
        required=True,
        style={'input_type': 'password'}  # 在Swagger UI中显示为密码输入框
    )
    verifcation = serializers.CharField(
        max_length=10,
        help_text="验证码",
        required=False,
        allow_blank=True
    )
    client_time = serializers.IntegerField(
        help_text="客户端时间戳",
        required=True
    )

    class Meta:
        # 不绑定到具体模型，这是一个纯粹的请求序列化器
        fields = ['username', 'password', 'verifcation', 'client_time']
        # API文档示例值（仅用于文档展示）
        swagger_schema_fields = {
            "example": {
                "username": "zhous",
                "password": "admin",
                "verifcation": "1234",
                "client_time": 1684489627117
            }
        }


# 强制登录请求序列化器
class ForceLoginRequestSerializer(LoginRequestSerializer):
    """
    强制登录请求的序列化器，继承自登录请求序列化器
    """
    # insuranceName = serializers.CharField(
    #     max_length=100,
    #     help_text="保险名称",
    #     required=False,
    #     allow_blank=True
    # )

    class Meta:
        fields = ['username', 'password', 'verifcation', 'client_time']
        # API文档示例值（仅用于文档展示）
        swagger_schema_fields = {
            "example": {
                "username": "zhous",
                "password": "admin",
                "verifcation": "1234",
                "client_time": 1684489627117
            }
        }


# 单点登录请求序列化器
class SSOLoginRequestSerializer(serializers.Serializer):
    """
    单点登录请求的序列化器
    """
    ssoToken = serializers.CharField(
        max_length=500,
        help_text="单点登录令牌",
        required=True
    )
    
    class Meta:
        fields = ['ssoToken']
        # API文档示例值（仅用于文档展示）
        swagger_schema_fields = {
            "example": {
                "ssoToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VybmFtZSI6ImFkbWluIn0.example_token"
            }
        }


# 退出登录请求序列化器
class LogoutRequestSerializer(serializers.Serializer):
    """
    退出登录请求的序列化器
    """
    username = serializers.CharField(
        max_length=150,
        help_text="用户名",
        required=True
    )
    userid = serializers.IntegerField(
        help_text="用户ID",
        required=True
    )
    
    class Meta:
        fields = ['username', 'userid']
        # API文档示例值（仅用于文档展示）
        swagger_schema_fields = {
            "example": {
                "username": "admin",
                "userid": 123
            }
        }


# 登录响应序列化器
class LoginResponseSerializer(serializers.Serializer):
    """
    登录响应的序列化器，用于文档展示
    """
    code = serializers.IntegerField(help_text="响应代码")
    success = serializers.BooleanField(help_text="是否成功",required=False)
    message = serializers.CharField(help_text="响应消息", required=False)
    token = serializers.CharField(help_text="令牌", required=False)
    data = serializers.DictField(help_text="响应数据", required=False)
    
    class Meta:
        fields = ['success', 'code', 'message', 'token', 'data']
        # API文档示例值（仅用于文档展示）
        swagger_schema_fields = {
            "example": {
                "code": 0,
                "success": True,
                "info": "用户登录成功！",
                "userid": 1817609406574606,
                "username": "zhous",
                "token": "5c9e724860e96e2eb55112213cb9d8ffa8bc0243"
            }
        }


# 用户信息表序列化器
class AuthUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = AuthUser
        fields = "__all__"


# 上传文件表序列器
class TtUploadFileDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtUploadFileData
        fields = "__all__"


# 行政区划表序列器
class TmDdistrictSerializer(serializers.ModelSerializer):
    class Meta:
        model = TmDdistrict
        fields = "__all__"


# 兵团区划表序列器
class TmBingtuanSerializer(serializers.ModelSerializer):
    class Meta:
        model = TmBingtuan
        fields = "__all__"


# 系统配置表序列器
class SysConfigSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysConfig
        fields = "__all__"


# 系统部门表序列器
class SysDepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysDepartment
        fields = "__all__"


# 系统日志表序列器
class SysLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysLog
        fields = "__all__"


# 系统菜单表序列器
class SysMenuSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysMenu
        fields = "__all__"


# 系统定制表序列器
class SysOssSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysOss
        fields = "__all__"


# 系统角色表序列器
class SysRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysRole
        fields = "__all__"


# 系统角色菜单表序列器
class SysRoleMenuSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysRoleMenu
        fields = "__all__"


# 系统用户表（已废弃）序列器
class SysUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysUser
        fields = "__all__"


# 系统用户角色表序列器
class SysUserRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysUserRole
        fields = "__all__"


# 系统用户token表序列器
class SysUserTokenSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysUserToken
        fields = "__all__"


class AuthUserpulldownSerializer(serializers.ModelSerializer):
    class Meta:
        model = AuthUser
        fields = ["id", "fullname"]


# 系统参数
class SysParamSerializer(serializers.ModelSerializer):
    class Meta:
        model = SysParam
        fields = "__all__"


# 标注符号数据表序列器
class TtAnnotationSysmbolDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtAnnotationSysmbolData
        fields = "__all__"


# 标注类型字典表序列器
class TtAnnotationTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtAnnotationType
        fields = "__all__"


# 要素标注数据表序列器
class TtFeatureAnnotationDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtFeatureAnnotationData
        fields = "__all__"

# 标注数据新增字段表序列器
class TtFeatureAnnotationNewFieldsSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtFeatureAnnotationeNewFields
        fields = "__all__"

# 敌我信息字典表序列器
class TtFriendFoeInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtFriendFoeInfo
        fields = "__all__"


# 情报来源字典表序列器
class TtIntelligenceSourceSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtIntelligenceSource
        fields = "__all__"

# 标注点类型数据表序列器
class TtPointTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtPointType
        fields = "__all__"


# 字段类型数据表序列器
class TtFieldTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtFieldType
        fields = "__all__"


# POI数据表序列器
class TtPoiDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtPoiData
        fields = "__all__"

# 服务数据表序列器
class TtServiceDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtServiceData
        fields = "__all__"

# 服务接口类型序列器
class TtServiceInterfaceTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtServiceInterfaceType
        fields = "__all__"


# 视口书签数据表序列器
class TtViewBookmarkDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtViewBookmarkData
        fields = "__all__"


# 重点点位区域数据表序列器
class TtKeyPointAreaDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = TtKeyPointAreaData
        fields = "__all__"


# ==================== 无人机业务相关序列化器 ====================

# 无人机任务表序列化器
class TtDroneTasksSerializer(serializers.ModelSerializer):
    """无人机任务表序列化器"""

    # 只读字段，用于显示关联对象的详细信息
    selected_drone_info = serializers.SerializerMethodField(read_only=True)
    reviewer_info = serializers.SerializerMethodField(read_only=True)
    created_by_info = serializers.SerializerMethodField(read_only=True)
    updated_by_info = serializers.SerializerMethodField(read_only=True)
    task_status_display = serializers.CharField(source='get_task_status_display', read_only=True)
    task_purpose_display = serializers.CharField(source='get_task_purpose_display', read_only=True)
    task_shape_display = serializers.CharField(source='get_task_shape_display', read_only=True)
    task_urgency_display = serializers.CharField(source='get_task_urgency_display', read_only=True)
    review_status_display = serializers.CharField(source='get_review_status_display', read_only=True)

    class Meta:
        model = TtDroneTasks
        exclude = ['created_by_id', 'updated_by_id', 'reviewer_id']

    def get_selected_drone_info(self, obj):
        """获取关联无人机的基本信息"""
        if obj.selected_drone:
            return {
                'drone_id': obj.selected_drone.drone_id,
                'drone_serial': obj.selected_drone.drone_serial,
                'brand': obj.selected_drone.brand,
                'model': obj.selected_drone.model,
                'status': obj.selected_drone.status
            }
        return None

    def get_reviewer_info(self, obj):
        """获取审核人信息"""
        return obj.reviewer_id

    def get_created_by_info(self, obj):
        """获取创建人信息"""
        return obj.created_by_id

    def get_updated_by_info(self, obj):
        """获取更新人信息"""
        return obj.updated_by_id


# 无人机设备基础信息表序列化器
class TtDroneBasicInfoSerializer(serializers.ModelSerializer):
    """无人机设备基础信息表序列化器"""

    # 只读字段，用于显示枚举值的中文名称
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    brand_display = serializers.CharField(source='get_brand_display', read_only=True)
    deployment_status_display = serializers.CharField(source='get_deployment_status_display', read_only=True)

    # 关联任务数量
    assigned_tasks_count = serializers.SerializerMethodField(read_only=True)

    # 连接状态信息
    is_connected = serializers.SerializerMethodField(read_only=True)

    # 用户信息
    created_by_info = serializers.SerializerMethodField(read_only=True)
    updated_by_info = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = TtDroneBasicInfo
        exclude = ['created_by_id', 'updated_by_id']

    def get_assigned_tasks_count(self, obj):
        """获取分配给该无人机的任务数量"""
        return obj.assigned_tasks.count()

    def get_is_connected(self, obj):
        """判断无人机是否在线连接"""
        return obj.connection_status == 'connected'

    def get_created_by_info(self, obj):
        """获取创建人信息"""
        return obj.created_by_id

    def get_updated_by_info(self, obj):
        """获取更新人信息"""
        return obj.updated_by_id


# 告警记录表序列化器
class TtDroneAlertsSerializer(serializers.ModelSerializer):
    """告警记录表序列化器"""

    # 只读字段，用于显示关联对象的详细信息
    task_info = serializers.SerializerMethodField(read_only=True)
    drone_info = serializers.SerializerMethodField(read_only=True)
    alert_parent_type_display = serializers.CharField(source='get_alert_parent_type_display', read_only=True)
    alert_type_display = serializers.CharField(source='get_alert_type_id_display', read_only=True)
    alert_level_display = serializers.CharField(source='get_alert_level_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    # 用户信息
    created_by_info = serializers.SerializerMethodField(read_only=True)
    updated_by_info = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = TtDroneAlerts
        exclude = ['created_by_id', 'updated_by_id']

    def get_task_info(self, obj):
        """获取关联任务的基本信息"""
        if obj.task:
            return {
                'id': obj.task.id,
                'task_name': obj.task.task_name,
                'task_status': obj.task.task_status
            }
        return None

    def get_drone_info(self, obj):
        """获取关联无人机的基本信息"""
        if obj.drone:
            return {
                'drone_id': obj.drone.drone_id,
                'drone_serial': obj.drone.drone_serial,
                'brand': obj.drone.brand,
                'model': obj.drone.model
            }
        return None

    def get_created_by_info(self, obj):
        """获取创建人信息"""
        return obj.created_by_id

    def get_updated_by_info(self, obj):
        """获取更新人信息"""
        return obj.updated_by_id


# 航线排期表序列化器
class TtFlightScheduleSerializer(serializers.ModelSerializer):
    """航线排期表序列化器"""

    # 只读字段，用于显示关联对象的详细信息
    task_info = serializers.SerializerMethodField(read_only=True)
    drone_info = serializers.SerializerMethodField(read_only=True)
    schedule_status_display = serializers.CharField(source='get_schedule_status_display', read_only=True)

    # 计算字段
    actual_duration = serializers.SerializerMethodField(read_only=True)
    duration_variance = serializers.SerializerMethodField(read_only=True)

    # 用户信息
    created_by_info = serializers.SerializerMethodField(read_only=True)
    updated_by_info = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = TtFlightSchedule
        exclude = ['created_by_id', 'updated_by_id']

    def get_task_info(self, obj):
        """获取关联任务的基本信息"""
        if obj.task:
            return {
                'task_id': obj.task.id,
                'task_name': obj.task.task_name,
                'task_purpose': obj.task.task_purpose,
                'task_status': obj.task.task_status
            }
        return None

    def get_drone_info(self, obj):
        """获取执行无人机的基本信息"""
        if obj.drone:
            return {
                'drone_id': obj.drone.drone_id,
                'drone_serial': obj.drone.drone_serial,
                'brand': obj.drone.brand,
                'model': obj.drone.model,
                'status': obj.drone.status
            }
        return None

    def get_actual_duration(self, obj):
        """计算实际飞行时长（分钟）"""
        if obj.actual_start_time and obj.actual_end_time:
            duration = obj.actual_end_time - obj.actual_start_time
            return int(duration.total_seconds() / 60)
        return None

    def get_duration_variance(self, obj):
        """计算时长偏差（实际时长 - 预计时长）"""
        actual = self.get_actual_duration(obj)
        if actual is not None:
            return actual - obj.estimated_duration
        return None

    def get_created_by_info(self, obj):
        """获取创建人信息"""
        return obj.created_by_id

    def get_updated_by_info(self, obj):
        """获取更新人信息"""
        return obj.updated_by_id


# ==================== API请求/响应序列化器 ====================

# 任务创建请求序列化器
class TaskCreateRequestSerializer(serializers.Serializer):
    """任务创建请求序列化器"""
    task_name = serializers.CharField(max_length=255, help_text="任务名称")
    task_purpose = serializers.ChoiceField(
        choices=TtDroneTasks.TaskPurpose.choices,
        help_text="任务用途"
    )
    task_shape = serializers.ChoiceField(
        choices=TtDroneTasks.TaskShape.choices,
        help_text="任务形状"
    )
    task_urgency = serializers.ChoiceField(
        choices=TtDroneTasks.TaskUrgency.choices,
        default='medium',
        help_text="紧急程度"
    )
    center_coordinates = serializers.CharField(
        max_length=50,
        required=False,
        help_text="中心坐标，格式：经度,纬度"
    )
    task_shape_points = serializers.CharField(
        required=False,
        help_text="GeoJSON格式的几何数据"
    )
    selected_drone_id = serializers.IntegerField(
        required=False,
        help_text="选择的无人机ID"
    )
    remark = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="备注信息"
    )

    class Meta:
        fields = [
            'task_name', 'task_purpose', 'task_shape', 'task_urgency',
            'center_coordinates', 'task_shape_points', 'selected_drone_id', 'remark'
        ]


# 导入新的通用响应序列化器
from my_app.serializers.common import CommonResponseSerializer